# 树莓派L298N电机控制系统

这是一个基于树莓派5和L298N电机驱动器的双电机控制系统，包含现代化的Web前端界面和Python后端服务。

## 🔧 硬件配置

### L298N引脚连接
| L298N引脚 | 树莓派GPIO | 功能描述 |
|-----------|------------|----------|
| ENA       | GPIO 11    | 左电机PWM使能 |
| ENB       | GPIO 12    | 右电机PWM使能 |
| IN1       | GPIO 13    | 左电机前进控制 |
| IN2       | GPIO 15    | 左电机后退控制 |
| IN3       | GPIO 16    | 右电机前进控制 |
| IN4       | GPIO 18    | 右电机后退控制 |

### 电源连接
- L298N的VCC连接到5V电源
- L298N的GND连接到树莓派GND
- 电机电源根据电机规格连接到L298N的电机电源输入

## 📁 项目结构

```
html-service/
├── html/                    # 前端文件
│   ├── index.html          # 主页面
│   ├── style.css           # 样式文件
│   └── script.js           # JavaScript逻辑
├── service/                # 后端服务
│   ├── motor_controller.py # 主服务文件
│   ├── requirements.txt    # Python依赖
│   └── start_service.sh    # 启动脚本
└── README.md               # 说明文档
```

## 🚀 安装和运行

### 1. 准备树莓派环境

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Python3和pip
sudo apt install python3 python3-pip -y

# 将用户添加到gpio组（需要重新登录生效）
sudo usermod -a -G gpio $USER
```

### 2. 安装后端服务

```bash
# 进入后端目录
cd html-service/service

# 安装Python依赖
pip3 install -r requirements.txt

# 给启动脚本执行权限
chmod +x start_service.sh
```

### 3. 启动服务

```bash
# 方式1: 使用启动脚本
./start_service.sh

# 方式2: 直接运行Python服务
python3 motor_controller.py
```

### 4. 访问前端界面

1. 将`html`文件夹中的所有文件复制到Web服务器目录
2. 或者直接用浏览器打开`index.html`文件
3. 确保前端页面能访问到后端服务（默认端口5000）

## 🎮 功能特性

### 前端界面
- **现代化UI设计**: 渐变背景、圆角按钮、响应式布局
- **速度控制**: 10%步进的加速/减速按钮，实时速度显示
- **方向控制**: 前进、后退、左转、右转按钮
- **状态监控**: 连接状态、电机状态、当前动作显示
- **触摸支持**: 支持移动设备触摸操作
- **安全设计**: 按住控制，松开停止

### 后端服务
- **GPIO Zero库**: 使用现代化的GPIO控制库
- **PWM速度控制**: 支持0-100%的精确速度调节
- **RESTful API**: 标准的HTTP接口
- **错误处理**: 完善的异常处理和日志记录
- **安全停止**: 优雅的服务关闭和GPIO清理
- **模拟模式**: 在非树莓派环境下可运行测试

## 📡 API接口

### 获取状态
```http
GET /status
```

### 设置速度
```http
POST /speed
Content-Type: application/json

{
    "speed": 70
}
```

### 控制移动
```http
POST /move
Content-Type: application/json

{
    "action": "forward",
    "speed": 50
}
```

支持的动作: `forward`, `backward`, `left`, `right`, `stop`

### 紧急停止
```http
POST /emergency_stop
```

## 🔒 安全注意事项

1. **电源安全**: 确保电机电源电压与电机匹配
2. **接线检查**: 仔细检查所有连接，避免短路
3. **GPIO权限**: 确保用户在gpio组中
4. **紧急停止**: 随时准备断开电源
5. **测试环境**: 先在安全环境下测试

## 🛠️ 故障排除

### 常见问题

1. **GPIO权限错误**
   ```bash
   sudo usermod -a -G gpio $USER
   # 重新登录后生效
   ```

2. **依赖安装失败**
   ```bash
   sudo apt install python3-dev python3-pip
   pip3 install --upgrade pip
   ```

3. **电机不转动**
   - 检查电源连接
   - 检查引脚连接
   - 检查电机电源电压

4. **前端无法连接后端**
   - 检查后端服务是否启动
   - 检查防火墙设置
   - 确认IP地址和端口

## 📝 开发说明

### 自定义配置
可以在`motor_controller.py`中修改引脚配置：

```python
self.pin_config = {
    'left_motor': {
        'forward': 13,    # IN1
        'backward': 15,   # IN2
        'enable': 11      # ENA
    },
    'right_motor': {
        'forward': 16,    # IN3
        'backward': 18,   # IN4
        'enable': 12      # ENB
    }
}
```

### 扩展功能
- 添加传感器反馈
- 实现自动导航
- 添加摄像头控制
- 集成语音控制

## 📄 许可证

本项目仅供学习和研究使用。使用时请注意安全，作者不承担任何因使用本代码造成的损失。
