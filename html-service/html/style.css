* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

header h1 {
    color: #4a5568;
    margin-bottom: 10px;
    font-size: 2.2em;
}

.status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #fbbf24;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: #10b981;
}

.status-dot.disconnected {
    background: #ef4444;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

main {
    display: grid;
    gap: 25px;
}

section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

section h2, section h3 {
    color: #4a5568;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.5em;
}

/* 速度控制样式 */
.speed-display {
    text-align: center;
    margin-bottom: 20px;
}

.speed-label {
    display: block;
    font-size: 1.1em;
    color: #6b7280;
    margin-bottom: 5px;
}

.speed-value {
    font-size: 2.5em;
    font-weight: bold;
    color: #3b82f6;
}

.speed-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 20px;
}

.speed-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px 25px;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-size: 1em;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 100px;
}

.speed-btn.increase {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.speed-btn.decrease {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.speed-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.speed-btn:active {
    transform: translateY(0);
}

.speed-bar {
    width: 100%;
    height: 12px;
    background: #e5e7eb;
    border-radius: 6px;
    overflow: hidden;
}

.speed-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 6px;
    transition: width 0.3s ease;
    width: 50%;
}

/* 方向控制样式 */
.control-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: center;
}

.control-row {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.direction-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 20px;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    font-size: 1em;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 100px;
    min-height: 80px;
}

.direction-btn.forward {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.direction-btn.backward {
    background: linear-gradient(135deg, #6366f1, #4f46e5);
    color: white;
}

.direction-btn.left, .direction-btn.right {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

.direction-btn.stop {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.direction-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.direction-btn:active {
    transform: translateY(-1px);
}

.direction-btn.active {
    transform: scale(0.95);
    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-icon {
    font-size: 1.5em;
}

.btn-text {
    font-size: 0.9em;
}

/* 信息面板样式 */
.info-grid {
    display: grid;
    gap: 15px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #3b82f6;
}

.info-label {
    font-weight: 600;
    color: #4b5563;
}

.info-value {
    font-weight: 500;
    color: #1f2937;
    padding: 4px 12px;
    background: #e0e7ff;
    border-radius: 6px;
}

footer {
    text-align: center;
    margin-top: 30px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    color: #6b7280;
    font-size: 0.9em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .control-row {
        flex-wrap: wrap;
    }
    
    .direction-btn {
        min-width: 80px;
        min-height: 70px;
        padding: 15px;
    }
    
    .speed-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .speed-btn {
        width: 200px;
    }
}
