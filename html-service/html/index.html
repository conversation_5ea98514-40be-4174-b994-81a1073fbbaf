<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>树莓派电机控制器</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🤖 树莓派电机控制器</h1>
            <div class="status-indicator">
                <span class="status-dot" id="statusDot"></span>
                <span id="statusText">连接中...</span>
            </div>
        </header>

        <main>
            <!-- 速度控制区域 -->
            <section class="speed-control">
                <h2>速度控制</h2>
                <div class="speed-display">
                    <span class="speed-label">当前速度</span>
                    <span class="speed-value" id="speedValue">50%</span>
                </div>
                <div class="speed-buttons">
                    <button class="speed-btn decrease" id="decreaseBtn">
                        <span class="btn-icon">➖</span>
                        <span class="btn-text">减速</span>
                    </button>
                    <button class="speed-btn increase" id="increaseBtn">
                        <span class="btn-icon">➕</span>
                        <span class="btn-text">加速</span>
                    </button>
                </div>
                <div class="speed-bar">
                    <div class="speed-fill" id="speedFill"></div>
                </div>
            </section>

            <!-- 方向控制区域 -->
            <section class="direction-control">
                <h2>方向控制</h2>
                <div class="control-grid">
                    <div class="control-row">
                        <button class="direction-btn forward" id="forwardBtn">
                            <span class="btn-icon">⬆️</span>
                            <span class="btn-text">前进</span>
                        </button>
                    </div>
                    <div class="control-row">
                        <button class="direction-btn left" id="leftBtn">
                            <span class="btn-icon">⬅️</span>
                            <span class="btn-text">左转</span>
                        </button>
                        <button class="direction-btn stop" id="stopBtn">
                            <span class="btn-icon">⏹️</span>
                            <span class="btn-text">停止</span>
                        </button>
                        <button class="direction-btn right" id="rightBtn">
                            <span class="btn-icon">➡️</span>
                            <span class="btn-text">右转</span>
                        </button>
                    </div>
                    <div class="control-row">
                        <button class="direction-btn backward" id="backwardBtn">
                            <span class="btn-icon">⬇️</span>
                            <span class="btn-text">后退</span>
                        </button>
                    </div>
                </div>
            </section>

            <!-- 信息显示区域 -->
            <section class="info-panel">
                <h3>控制信息</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">左电机状态:</span>
                        <span class="info-value" id="leftMotorStatus">停止</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">右电机状态:</span>
                        <span class="info-value" id="rightMotorStatus">停止</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">当前动作:</span>
                        <span class="info-value" id="currentAction">待机</span>
                    </div>
                </div>
            </section>
        </main>

        <footer>
            <p>🔧 L298N引脚配置: ENA=11, ENB=12, IN1=13, IN2=15, IN3=16, IN4=18</p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
