class MotorController {
    constructor() {
        this.speed = 50; // 初始速度50%
        this.isConnected = false;
        this.isActionInProgress = false; // 防止重复触发
        // 动态获取后端服务地址，支持树莓派热点访问
        this.baseUrl = `http://${window.location.hostname}:5000`;

        this.initializeElements();
        this.bindEvents();
        this.updateSpeedDisplay();
        // 移除连接检查
        this.isConnected = true; // 假设总是连接的
    }

    initializeElements() {
        // 速度控制元素
        this.speedValue = document.getElementById('speedValue');
        this.speedFill = document.getElementById('speedFill');
        this.increaseBtn = document.getElementById('increaseBtn');
        this.decreaseBtn = document.getElementById('decreaseBtn');

        // 方向控制元素
        this.forwardBtn = document.getElementById('forwardBtn');
        this.backwardBtn = document.getElementById('backwardBtn');
        this.leftBtn = document.getElementById('leftBtn');
        this.rightBtn = document.getElementById('rightBtn');
        this.stopBtn = document.getElementById('stopBtn');
    }

    bindEvents() {
        // 速度控制事件
        this.increaseBtn.addEventListener('click', () => this.changeSpeed(10));
        this.decreaseBtn.addEventListener('click', () => this.changeSpeed(-10));

        // 方向控制事件 - 简化事件绑定
        const buttons = [
            { btn: this.forwardBtn, action: 'forward' },
            { btn: this.backwardBtn, action: 'backward' },
            { btn: this.leftBtn, action: 'left' },
            { btn: this.rightBtn, action: 'right' }
        ];

        buttons.forEach(({ btn, action }) => {
            this.bindButtonEvents(btn, action);
        });

        // 停止按钮
        this.stopBtn.addEventListener('click', () => this.stopAction());
    }

    bindButtonEvents(button, action) {
        let isPressed = false;

        const startAction = (e) => {
            e.preventDefault();
            e.stopPropagation();
            if (!isPressed && !this.isActionInProgress) {
                isPressed = true;
                this.startAction(action);
            }
        };

        const stopAction = (e) => {
            if (e) {
                e.preventDefault();
                e.stopPropagation();
            }
            if (isPressed) {
                isPressed = false;
                this.stopAction();
            }
        };

        // 检测是否为触摸设备
        if ('ontouchstart' in window) {
            // 触摸设备
            button.addEventListener('touchstart', startAction, { passive: false });
            button.addEventListener('touchend', stopAction, { passive: false });
            button.addEventListener('touchcancel', stopAction, { passive: false });
        } else {
            // 桌面设备
            button.addEventListener('mousedown', startAction);
            button.addEventListener('mouseup', stopAction);
            button.addEventListener('mouseleave', stopAction);
        }
    }



    changeSpeed(delta) {
        const newSpeed = Math.max(0, Math.min(100, this.speed + delta));
        if (newSpeed !== this.speed) {
            this.speed = newSpeed;
            this.updateSpeedDisplay();
            this.sendCommand('speed', { speed: this.speed });
        }
    }

    updateSpeedDisplay() {
        this.speedValue.textContent = `${this.speed}%`;
        this.speedFill.style.width = `${this.speed}%`;
        
        // 更新速度条颜色
        if (this.speed < 30) {
            this.speedFill.style.background = 'linear-gradient(90deg, #10b981, #059669)';
        } else if (this.speed < 70) {
            this.speedFill.style.background = 'linear-gradient(90deg, #3b82f6, #1d4ed8)';
        } else {
            this.speedFill.style.background = 'linear-gradient(90deg, #ef4444, #dc2626)';
        }
    }

    startAction(action) {
        // 防止重复触发
        if (this.isActionInProgress) {
            return;
        }

        this.isActionInProgress = true;
        this.clearActiveButtons();
        const button = document.getElementById(action + 'Btn');
        button.classList.add('active');

        this.updateActionStatus(action);
        this.sendCommand('move', { action: action, speed: this.speed });
    }

    stopAction() {
        // 防止重复触发
        if (!this.isActionInProgress) {
            return;
        }

        this.isActionInProgress = false;
        this.clearActiveButtons();
        this.updateActionStatus('stop');
        this.sendCommand('move', { action: 'stop', speed: 0 });
    }

    clearActiveButtons() {
        const buttons = document.querySelectorAll('.direction-btn');
        buttons.forEach(btn => btn.classList.remove('active'));
    }

    updateActionStatus(action) {
        // 简化状态更新，只在控制台输出
        console.log(`当前动作: ${action}`);
    }

    async sendCommand(endpoint, data) {
        try {
            console.log(`发送命令: ${endpoint}`, data);
            const response = await fetch(`${this.baseUrl}/${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('命令发送成功:', result);

        } catch (error) {
            console.error('发送命令失败:', error);
        }
    }


}

// 页面加载完成后初始化控制器
document.addEventListener('DOMContentLoaded', () => {
    new MotorController();
});

// 防止页面刷新时的意外操作
window.addEventListener('beforeunload', () => {
    // 发送停止命令
    fetch(`http://${window.location.hostname}:5000/move`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'stop', speed: 0 })
    }).catch(() => {});
});
