class MotorController {
    constructor() {
        this.speed = 50; // 初始速度50%
        this.isConnected = false;
        this.baseUrl = 'http://localhost:5000'; // 后端服务地址
        
        this.initializeElements();
        this.bindEvents();
        this.updateSpeedDisplay();
        this.checkConnection();
    }

    initializeElements() {
        // 速度控制元素
        this.speedValue = document.getElementById('speedValue');
        this.speedFill = document.getElementById('speedFill');
        this.increaseBtn = document.getElementById('increaseBtn');
        this.decreaseBtn = document.getElementById('decreaseBtn');
        
        // 方向控制元素
        this.forwardBtn = document.getElementById('forwardBtn');
        this.backwardBtn = document.getElementById('backwardBtn');
        this.leftBtn = document.getElementById('leftBtn');
        this.rightBtn = document.getElementById('rightBtn');
        this.stopBtn = document.getElementById('stopBtn');
        
        // 状态显示元素
        this.statusDot = document.getElementById('statusDot');
        this.statusText = document.getElementById('statusText');
        this.leftMotorStatus = document.getElementById('leftMotorStatus');
        this.rightMotorStatus = document.getElementById('rightMotorStatus');
        this.currentAction = document.getElementById('currentAction');
    }

    bindEvents() {
        // 速度控制事件
        this.increaseBtn.addEventListener('click', () => this.changeSpeed(10));
        this.decreaseBtn.addEventListener('click', () => this.changeSpeed(-10));
        
        // 方向控制事件 - 使用mousedown/mouseup实现按住控制
        this.forwardBtn.addEventListener('mousedown', () => this.startAction('forward'));
        this.forwardBtn.addEventListener('mouseup', () => this.stopAction());
        this.forwardBtn.addEventListener('mouseleave', () => this.stopAction());
        
        this.backwardBtn.addEventListener('mousedown', () => this.startAction('backward'));
        this.backwardBtn.addEventListener('mouseup', () => this.stopAction());
        this.backwardBtn.addEventListener('mouseleave', () => this.stopAction());
        
        this.leftBtn.addEventListener('mousedown', () => this.startAction('left'));
        this.leftBtn.addEventListener('mouseup', () => this.stopAction());
        this.leftBtn.addEventListener('mouseleave', () => this.stopAction());
        
        this.rightBtn.addEventListener('mousedown', () => this.startAction('right'));
        this.rightBtn.addEventListener('mouseup', () => this.stopAction());
        this.rightBtn.addEventListener('mouseleave', () => this.stopAction());
        
        this.stopBtn.addEventListener('click', () => this.stopAction());
        
        // 触摸设备支持
        this.addTouchEvents();
    }

    addTouchEvents() {
        const buttons = [this.forwardBtn, this.backwardBtn, this.leftBtn, this.rightBtn];
        
        buttons.forEach(btn => {
            btn.addEventListener('touchstart', (e) => {
                e.preventDefault();
                const action = btn.id.replace('Btn', '');
                this.startAction(action);
            });
            
            btn.addEventListener('touchend', (e) => {
                e.preventDefault();
                this.stopAction();
            });
        });
    }

    changeSpeed(delta) {
        const newSpeed = Math.max(0, Math.min(100, this.speed + delta));
        if (newSpeed !== this.speed) {
            this.speed = newSpeed;
            this.updateSpeedDisplay();
            this.sendCommand('speed', { speed: this.speed });
        }
    }

    updateSpeedDisplay() {
        this.speedValue.textContent = `${this.speed}%`;
        this.speedFill.style.width = `${this.speed}%`;
        
        // 更新速度条颜色
        if (this.speed < 30) {
            this.speedFill.style.background = 'linear-gradient(90deg, #10b981, #059669)';
        } else if (this.speed < 70) {
            this.speedFill.style.background = 'linear-gradient(90deg, #3b82f6, #1d4ed8)';
        } else {
            this.speedFill.style.background = 'linear-gradient(90deg, #ef4444, #dc2626)';
        }
    }

    startAction(action) {
        this.clearActiveButtons();
        const button = document.getElementById(action + 'Btn');
        button.classList.add('active');
        
        this.updateActionStatus(action);
        this.sendCommand('move', { action: action, speed: this.speed });
    }

    stopAction() {
        this.clearActiveButtons();
        this.updateActionStatus('stop');
        this.sendCommand('move', { action: 'stop', speed: 0 });
    }

    clearActiveButtons() {
        const buttons = document.querySelectorAll('.direction-btn');
        buttons.forEach(btn => btn.classList.remove('active'));
    }

    updateActionStatus(action) {
        const actionMap = {
            'forward': '前进',
            'backward': '后退',
            'left': '左转',
            'right': '右转',
            'stop': '停止'
        };
        
        this.currentAction.textContent = actionMap[action] || '待机';
        
        // 更新电机状态显示
        switch(action) {
            case 'forward':
                this.leftMotorStatus.textContent = '前进';
                this.rightMotorStatus.textContent = '前进';
                break;
            case 'backward':
                this.leftMotorStatus.textContent = '后退';
                this.rightMotorStatus.textContent = '后退';
                break;
            case 'left':
                this.leftMotorStatus.textContent = '后退';
                this.rightMotorStatus.textContent = '前进';
                break;
            case 'right':
                this.leftMotorStatus.textContent = '前进';
                this.rightMotorStatus.textContent = '后退';
                break;
            default:
                this.leftMotorStatus.textContent = '停止';
                this.rightMotorStatus.textContent = '停止';
        }
    }

    async sendCommand(endpoint, data) {
        if (!this.isConnected) {
            console.warn('未连接到后端服务');
            return;
        }
        
        try {
            const response = await fetch(`${this.baseUrl}/${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            console.log('命令发送成功:', result);
            
        } catch (error) {
            console.error('发送命令失败:', error);
            this.updateConnectionStatus(false);
        }
    }

    async checkConnection() {
        try {
            const response = await fetch(`${this.baseUrl}/status`, {
                method: 'GET',
                timeout: 5000
            });
            
            if (response.ok) {
                this.updateConnectionStatus(true);
            } else {
                this.updateConnectionStatus(false);
            }
        } catch (error) {
            this.updateConnectionStatus(false);
        }
        
        // 每5秒检查一次连接状态
        setTimeout(() => this.checkConnection(), 5000);
    }

    updateConnectionStatus(connected) {
        this.isConnected = connected;
        
        if (connected) {
            this.statusDot.className = 'status-dot connected';
            this.statusText.textContent = '已连接';
        } else {
            this.statusDot.className = 'status-dot disconnected';
            this.statusText.textContent = '连接断开';
        }
    }
}

// 页面加载完成后初始化控制器
document.addEventListener('DOMContentLoaded', () => {
    new MotorController();
});

// 防止页面刷新时的意外操作
window.addEventListener('beforeunload', () => {
    // 发送停止命令
    fetch('http://localhost:5000/move', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'stop', speed: 0 })
    }).catch(() => {});
});
