#!/usr/bin/env python3
"""
简单的HTTP服务器，用于托管前端文件
支持在树莓派上运行，手机通过热点访问
"""

import http.server
import socketserver
import os
import sys
from pathlib import Path

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器"""
    
    def end_headers(self):
        # 添加CORS头，允许跨域访问
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        # 处理预检请求
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        # 自定义日志格式
        print(f"[{self.log_date_time_string()}] {format % args}")

def get_local_ip():
    """获取本机IP地址"""
    import socket
    try:
        # 连接到一个远程地址来获取本机IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except Exception:
        return "127.0.0.1"

def main():
    # 设置端口
    PORT = 8080
    
    # 确保在正确的目录中运行
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print("=" * 50)
    print("🌐 前端Web服务器启动中...")
    print("=" * 50)
    
    # 检查文件是否存在
    required_files = ['index.html', 'style.css', 'script.js']
    missing_files = []
    
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 错误: 缺少必要文件: {', '.join(missing_files)}")
        sys.exit(1)
    
    # 获取IP地址
    local_ip = get_local_ip()
    
    try:
        # 创建服务器，绑定到所有网络接口
        with socketserver.TCPServer(("0.0.0.0", PORT), CustomHTTPRequestHandler) as httpd:
            httpd.allow_reuse_address = True  # 允许地址重用

            print(f"✅ 服务器启动成功!")
            print(f"📁 服务目录: {script_dir}")
            print(f"🌐 本地访问: http://localhost:{PORT}")
            print(f"📱 局域网访问: http://{local_ip}:{PORT}")
            print(f"🔗 树莓派热点访问: http://树莓派IP:{PORT}")
            print()
            print("📋 使用说明:")
            print("1. 确保后端服务(motor_controller.py)在端口5000运行")
            print("2. 在同一网络下的设备浏览器中访问上述地址")
            print("3. 按 Ctrl+C 停止服务")
            print("=" * 50)

            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 错误: 端口 {PORT} 已被占用")
            print("请检查是否有其他服务在使用该端口，或尝试使用其他端口")
        else:
            print(f"❌ 错误: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
