#!/usr/bin/env python3
"""
前端静态文件服务器
在端口8888上提供HTML文件服务
"""

import http.server
import socketserver
import os
import sys

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 添加CORS头部
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def main():
    # 设置服务器参数
    PORT = 8888
    HOST = '0.0.0.0'
    
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 切换到HTML文件目录
    os.chdir(current_dir)
    
    print(f"前端服务器启动中...")
    print(f"服务地址: http://{HOST}:{PORT}")
    print(f"文件目录: {current_dir}")
    print("=" * 40)
    print("按 Ctrl+C 停止服务器")
    
    try:
        # 创建服务器
        with socketserver.TCPServer((HOST, PORT), CustomHTTPRequestHandler) as httpd:
            print(f"前端服务器已启动在端口 {PORT}")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n正在关闭前端服务器...")
    except Exception as e:
        print(f"服务器启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
