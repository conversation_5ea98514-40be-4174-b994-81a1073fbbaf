#!/bin/bash

# 网络连接测试脚本

echo "🔍 网络连接诊断工具"
echo "===================="

# 获取IP地址
echo "📡 网络接口信息:"
ip addr show | grep -E "inet.*scope global" | awk '{print "   " $2 " (" $NF ")"}'

echo
echo "🌐 当前监听的端口:"
netstat -tuln | grep -E ":(5000|8080)" | awk '{print "   " $1 " " $4}'

echo
echo "🔥 防火墙状态:"
if command -v ufw >/dev/null 2>&1; then
    sudo ufw status | head -5
else
    echo "   UFW未安装"
fi

echo
echo "🔍 进程检查:"
if pgrep -f "motor_controller.py" >/dev/null; then
    echo "   ✅ 后端服务运行中 (PID: $(pgrep -f motor_controller.py))"
else
    echo "   ❌ 后端服务未运行"
fi

if pgrep -f "web_server.py" >/dev/null; then
    echo "   ✅ 前端服务运行中 (PID: $(pgrep -f web_server.py))"
else
    echo "   ❌ 前端服务未运行"
fi

echo
echo "🧪 本地连接测试:"
if curl -s http://localhost:8080 >/dev/null; then
    echo "   ✅ 本地8080端口可访问"
else
    echo "   ❌ 本地8080端口不可访问"
fi

if curl -s http://localhost:5000/status >/dev/null; then
    echo "   ✅ 本地5000端口可访问"
else
    echo "   ❌ 本地5000端口不可访问"
fi

echo
echo "💡 建议的解决方案:"
echo "1. 停止当前服务: 按 Ctrl+C"
echo "2. 开放防火墙端口:"
echo "   sudo ufw allow 8080"
echo "   sudo ufw allow 5000"
echo "3. 重新启动服务:"
echo "   ./start_all_services.sh"
echo "===================="
