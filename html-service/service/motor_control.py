#!/usr/bin/env python3
"""
L298N电机驱动控制程序
使用树莓派5控制L298N电机驱动模块
GPIO连接：ENA=17, ENB=18, IN1=27, IN2=22, IN3=23, IN4=24
"""

from gpiozero import Motor
import signal
import sys

class MotorController:
    def __init__(self):
        """
        初始化L298N电机控制器
        
        GPIO连接配置：
        - ENA (Enable A) = GPIO 17
        - ENB (Enable B) = GPIO 18  
        - IN1 = GPIO 27
        - IN2 = GPIO 22
        - IN3 = GPIO 23
        - IN4 = GPIO 24
        """
        # 根据您提供的示例和GPIO连接，配置两个电机
        # 电机A：使用ENA(17)作为enable，IN1(27)和IN2(22)控制方向
        self.motor_a = Motor(forward=27, backward=22, enable=17, pwm=True)
        
        # 电机B：使用ENB(18)作为enable，IN3(23)和IN4(24)控制方向  
        self.motor_b = Motor(forward=23, backward=24, enable=18, pwm=True)
        
        self.running = True
        self.current_speed = 0
        self.current_direction = "stop"
        
        # 设置信号处理器，用于优雅退出
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        print("L298N电机控制器已初始化")
        print("GPIO连接配置：")
        print("  ENA (Enable A) = GPIO 17")
        print("  ENB (Enable B) = GPIO 18")
        print("  IN1 = GPIO 27")
        print("  IN2 = GPIO 22") 
        print("  IN3 = GPIO 23")
        print("  IN4 = GPIO 24")
    
    def signal_handler(self, signum, frame):
        """信号处理器，用于优雅退出程序"""
        print("\n正在停止电机...")
        self.stop_motors()
        self.running = False
        sys.exit(0)
    
    def stop_motors(self):
        """停止所有电机"""
        self.motor_a.stop()
        self.motor_b.stop()
        self.current_speed = 0
        self.current_direction = "stop"
        print("电机已停止")
    
    def set_motor_speed(self, speed_level, direction="forward"):
        """
        设置电机速度
        
        Args:
            speed_level (int): 速度等级 0-10，0为停止
            direction (str): 方向 "forward" 或 "backward"
        """
        if speed_level == 0:
            self.stop_motors()
            return
        
        if not (1 <= speed_level <= 10):
            print("错误：速度等级必须在1-10之间")
            return
        
        # 将1-10的等级转换为0.1-1.0的PWM占空比
        pwm_value = speed_level / 10.0
        self.current_speed = speed_level
        self.current_direction = direction
        
        if direction == "forward":
            self.motor_a.forward(speed=pwm_value)
            self.motor_b.forward(speed=pwm_value)
            print(f"电机前进，速度等级：{speed_level}/10 (PWM: {pwm_value:.1f})")
        elif direction == "backward":
            self.motor_a.backward(speed=pwm_value)
            self.motor_b.backward(speed=pwm_value)
            print(f"电机后退，速度等级：{speed_level}/10 (PWM: {pwm_value:.1f})")
        else:
            print("错误：方向必须是 'forward' 或 'backward'")
    
    def turn_left(self, speed_level):
        """左转：左电机后退，右电机前进"""
        if not (1 <= speed_level <= 10):
            print("错误：速度等级必须在1-10之间")
            return
        
        pwm_value = speed_level / 10.0
        self.motor_a.backward(speed=pwm_value)  # 左电机后退
        self.motor_b.forward(speed=pwm_value)   # 右电机前进
        self.current_speed = speed_level
        self.current_direction = "left"
        print(f"左转，速度等级：{speed_level}/10 (PWM: {pwm_value:.1f})")
    
    def turn_right(self, speed_level):
        """右转：左电机前进，右电机后退"""
        if not (1 <= speed_level <= 10):
            print("错误：速度等级必须在1-10之间")
            return
        
        pwm_value = speed_level / 10.0
        self.motor_a.forward(speed=pwm_value)   # 左电机前进
        self.motor_b.backward(speed=pwm_value)  # 右电机后退
        self.current_speed = speed_level
        self.current_direction = "right"
        print(f"右转，速度等级：{speed_level}/10 (PWM: {pwm_value:.1f})")
    
    def show_status(self):
        """显示当前电机状态"""
        print(f"当前状态：{self.current_direction}，速度等级：{self.current_speed}/10")
    
    def cleanup(self):
        """清理资源"""
        self.stop_motors()
        self.motor_a.close()
        self.motor_b.close()

def print_help():
    """打印帮助信息"""
    print("\n" + "="*50)
    print("L298N电机控制命令：")
    print("="*50)
    print("0        - 停止电机")
    print("1-10     - 前进，数字代表速度等级(1=10%PWM, 10=100%PWM)")
    print("b1-b10   - 后退，数字代表速度等级")
    print("l1-l10   - 左转，数字代表速度等级")
    print("r1-r10   - 右转，数字代表速度等级")
    print("status   - 显示当前状态")
    print("help     - 显示此帮助信息")
    print("quit     - 退出程序")
    print("="*50)

def main():
    """主函数"""
    print("L298N电机驱动控制程序")
    print("=" * 30)
    
    # 创建电机控制器实例
    motor_controller = MotorController()
    
    # 显示帮助信息
    print_help()
    
    try:
        while motor_controller.running:
            try:
                # 获取用户输入
                user_input = input("\n请输入命令 (输入 'help' 查看帮助): ").strip().lower()
                
                if user_input == "quit" or user_input == "exit":
                    break
                elif user_input == "help":
                    print_help()
                elif user_input == "status":
                    motor_controller.show_status()
                elif user_input == "0":
                    motor_controller.set_motor_speed(0)
                elif user_input.isdigit() and 1 <= int(user_input) <= 10:
                    # 前进命令
                    speed = int(user_input)
                    motor_controller.set_motor_speed(speed, "forward")
                elif user_input.startswith('b') and user_input[1:].isdigit():
                    # 后退命令
                    speed = int(user_input[1:])
                    if 1 <= speed <= 10:
                        motor_controller.set_motor_speed(speed, "backward")
                    else:
                        print("错误：速度等级必须在1-10之间")
                elif user_input.startswith('l') and user_input[1:].isdigit():
                    # 左转命令
                    speed = int(user_input[1:])
                    if 1 <= speed <= 10:
                        motor_controller.turn_left(speed)
                    else:
                        print("错误：速度等级必须在1-10之间")
                elif user_input.startswith('r') and user_input[1:].isdigit():
                    # 右转命令
                    speed = int(user_input[1:])
                    if 1 <= speed <= 10:
                        motor_controller.turn_right(speed)
                    else:
                        print("错误：速度等级必须在1-10之间")
                else:
                    print("无效命令，输入 'help' 查看可用命令")
                    
            except ValueError:
                print("输入错误，请输入有效的数字")
            except KeyboardInterrupt:
                break
                
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        motor_controller.cleanup()
        print("程序已退出")

if __name__ == "__main__":
    main()
