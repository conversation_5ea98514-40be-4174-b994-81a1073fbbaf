#!/usr/bin/env python3
"""
树莓派L298N电机控制器后端服务
使用GPIO Zero库控制双电机
"""

from flask import Flask, request, jsonify
from flask_cors import CORS

import json
from datetime import datetime

try:
    from gpiozero import Motor
    GPIO_AVAILABLE = True
    print("GPIO Zero库已安装，运行在真实模式")
except ImportError:
    GPIO_AVAILABLE = False
    print("警告: GPIO Zero库未安装，运行在模拟模式")

class MotorController:
    def __init__(self):
        self.current_speed = 0.5  # 当前速度 (0.0 - 1.0)
        self.is_moving = False
        self.current_action = "stop"
        
        # L298N引脚配置
        self.pin_config = {
            'left_motor': {
                'forward': 13,    # IN1
                'backward': 15,   # IN2
                'enable': 11      # ENA
            },
            'right_motor': {
                'forward': 16,    # IN3
                'backward': 18,   # IN4
                'enable': 12      # ENB
            }
        }
        
        self.setup_motors()
      
    

    
    def setup_motors(self):
        """初始化电机"""
        global GPIO_AVAILABLE

        if GPIO_AVAILABLE:
            self.motor_left = Motor(
                forward=self.pin_config['left_motor']['forward'],
                backward=self.pin_config['left_motor']['backward'],
                enable=self.pin_config['left_motor']['enable'],
                pwm=True
            )

            self.motor_right = Motor(
                forward=self.pin_config['right_motor']['forward'],
                backward=self.pin_config['right_motor']['backward'],
                enable=self.pin_config['right_motor']['enable'],
                pwm=True
            )
            print("电机初始化成功")

              

          

     
    
    def set_speed(self, speed_percent):
        """设置电机速度 (0-100%)"""
        speed_percent = max(0, min(100, speed_percent))
        self.current_speed = speed_percent / 100.0
        return True
    
    def move_forward(self):
        """前进"""
        print(f"执行前进命令 - GPIO可用: {GPIO_AVAILABLE}, 速度: {self.current_speed}")
        if GPIO_AVAILABLE and self.motor_left and self.motor_right:
            self.motor_left.forward(speed=self.current_speed)
            self.motor_right.forward(speed=self.current_speed)
            print("GPIO命令已发送")
        else:
            print("模拟模式 - 前进")

        self.is_moving = True
        self.current_action = "forward"

    def move_backward(self):
        """后退"""
        print(f"执行后退命令 - GPIO可用: {GPIO_AVAILABLE}, 速度: {self.current_speed}")
        if GPIO_AVAILABLE and self.motor_left and self.motor_right:
            self.motor_left.backward(speed=self.current_speed)
            self.motor_right.backward(speed=self.current_speed)
            print("GPIO命令已发送")
        else:
            print("模拟模式 - 后退")

        self.is_moving = True
        self.current_action = "backward"

    def turn_left(self):
        """左转 (左轮后退，右轮前进)"""
        print(f"执行左转命令 - GPIO可用: {GPIO_AVAILABLE}, 速度: {self.current_speed}")
        if GPIO_AVAILABLE and self.motor_left and self.motor_right:
            self.motor_left.backward(speed=self.current_speed)
            self.motor_right.forward(speed=self.current_speed)
            print("GPIO命令已发送")
        else:
            print("模拟模式 - 左转")

        self.is_moving = True
        self.current_action = "left"

    def turn_right(self):
        """右转 (左轮前进，右轮后退)"""
        print(f"执行右转命令 - GPIO可用: {GPIO_AVAILABLE}, 速度: {self.current_speed}")
        if GPIO_AVAILABLE and self.motor_left and self.motor_right:
            self.motor_left.forward(speed=self.current_speed)
            self.motor_right.backward(speed=self.current_speed)
            print("GPIO命令已发送")
        else:
            print("模拟模式 - 右转")

        self.is_moving = True
        self.current_action = "right"

    def stop(self):
        """停止所有电机"""
        print(f"执行停止命令 - GPIO可用: {GPIO_AVAILABLE}")
        if GPIO_AVAILABLE and self.motor_left and self.motor_right:
            self.motor_left.stop()
            self.motor_right.stop()
            print("GPIO停止命令已发送")
        else:
            print("模拟模式 - 停止")

        self.is_moving = False
        self.current_action = "stop"
    
    def get_status(self):
        """获取当前状态"""
        return {
            'speed_percent': int(self.current_speed * 100),
            'speed_value': self.current_speed,
            'is_moving': self.is_moving,
            'current_action': self.current_action,
            'gpio_available': GPIO_AVAILABLE,
            'pin_config': self.pin_config,
            'timestamp': datetime.now().isoformat()
        }
    
    def cleanup(self):
        """清理资源"""
        self.stop()
        if GPIO_AVAILABLE and self.motor_left and self.motor_right:
            self.motor_left.close()
            self.motor_right.close()

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 创建电机控制器实例
motor_controller = MotorController()



@app.route('/speed', methods=['POST'])
def set_speed():
    """设置电机速度"""
    try:
        data = request.get_json()
        speed = data.get('speed', 50)
        
        if not isinstance(speed, (int, float)) or speed < 0 or speed > 100:
            return jsonify({
                'success': False,
                'error': '速度值必须在0-100之间'
            }), 400
        
        motor_controller.set_speed(speed)
        
        return jsonify({
            'success': True,
            'message': f'速度设置为 {speed}%',
            'data': {'speed': speed}
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/move', methods=['POST'])
def move():
    """控制电机移动"""
    try:
        data = request.get_json()
        action = data.get('action', 'stop')
        speed = data.get('speed', 50)

        print(f"收到move请求: action={action}, speed={speed}")

        # 设置速度
        if speed is not None:
            motor_controller.set_speed(speed)

        # 执行动作
        action_map = {
            'forward': motor_controller.move_forward,
            'backward': motor_controller.move_backward,
            'left': motor_controller.turn_left,
            'right': motor_controller.turn_right,
            'stop': motor_controller.stop
        }

        if action not in action_map:
            return jsonify({
                'success': False,
                'error': f'未知动作: {action}'
            }), 400

        print(f"执行动作: {action}")
        action_map[action]()

        return jsonify({
            'success': True,
            'message': f'执行动作: {action}',
            'data': {
                'action': action,
                'speed': speed
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/emergency_stop', methods=['POST'])
def emergency_stop():
    """紧急停止"""
    try:
        motor_controller.stop()
        return jsonify({
            'success': True,
            'message': '紧急停止执行成功'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': '接口不存在'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'error': '服务器内部错误'
    }), 500

def cleanup_handler(signum, frame):
    """信号处理器，用于优雅关闭"""
    print("\n正在关闭服务...")
    motor_controller.cleanup()
    exit(0)

if __name__ == '__main__':
    import signal
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, cleanup_handler)
    signal.signal(signal.SIGTERM, cleanup_handler)
    
    try:
        print("启动电机控制服务...")
        print("访问地址: http://localhost:5000")
        print("按 Ctrl+C 停止服务")
        
        app.run(
            host='0.0.0.0',  # 允许外部访问
            port=5000,
            debug=False,     # 生产环境关闭调试模式
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n服务已停止")
    finally:
        motor_controller.cleanup()
