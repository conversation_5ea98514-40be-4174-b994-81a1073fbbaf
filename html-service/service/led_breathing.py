#!/usr/bin/env python3
"""
LED呼吸灯效果控制程序
使用树莓派5的37引脚(GPIO 26)控制LED灯实现呼吸效果
"""

from gpiozero import PWMLED
from time import sleep
import signal
import sys

# 树莓派5的37引脚对应GPIO 26
LED_PIN = 26

class LEDBreathing:
    def __init__(self, pin=LED_PIN):
        """
        初始化LED呼吸灯控制器
        
        Args:
            pin (int): GPIO引脚号，默认为26(37引脚)
        """
        self.led = PWMLED(pin)
        self.running = True
        
        # 设置信号处理器，用于优雅退出
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """信号处理器，用于优雅退出程序"""
        print("\n正在停止LED呼吸灯...")
        self.running = False
        self.led.off()
        sys.exit(0)
    
    def breathing_effect(self, fade_in_time=2.0, fade_out_time=2.0, hold_time=0.5):
        """
        实现LED呼吸灯效果
        
        Args:
            fade_in_time (float): 渐亮时间(秒)
            fade_out_time (float): 渐暗时间(秒) 
            hold_time (float): 最亮和最暗时的保持时间(秒)
        """
        print(f"LED呼吸灯已启动 (GPIO {LED_PIN})")
        print("按 Ctrl+C 停止程序")
        
        try:
            while self.running:
                # 渐亮过程
                self.led.pulse(fade_in_time=fade_in_time, 
                              fade_out_time=fade_out_time,
                              n=1,  # 执行一次完整的呼吸周期
                              background=False)  # 阻塞执行
                
                if not self.running:
                    break
                    
                # 可以在这里添加短暂的暂停
                sleep(0.1)
                
        except KeyboardInterrupt:
            self.signal_handler(signal.SIGINT, None)
    
    def custom_breathing(self, steps=50, delay=0.05):
        """
        自定义呼吸效果实现
        
        Args:
            steps (int): 呼吸过程的步数
            delay (float): 每步之间的延迟时间
        """
        print(f"自定义LED呼吸灯已启动 (GPIO {LED_PIN})")
        print("按 Ctrl+C 停止程序")
        
        try:
            while self.running:
                # 渐亮过程
                for i in range(steps + 1):
                    if not self.running:
                        break
                    brightness = i / steps
                    self.led.value = brightness
                    sleep(delay)
                
                # 短暂保持最亮状态
                sleep(0.2)
                
                # 渐暗过程
                for i in range(steps, -1, -1):
                    if not self.running:
                        break
                    brightness = i / steps
                    self.led.value = brightness
                    sleep(delay)
                
                # 短暂保持最暗状态
                sleep(0.2)
                
        except KeyboardInterrupt:
            self.signal_handler(signal.SIGINT, None)
    
    def cleanup(self):
        """清理资源"""
        self.led.close()

def main():
    """主函数"""
    print("LED呼吸灯控制程序")
    print("=" * 30)
    
    # 创建LED控制器实例
    led_controller = LEDBreathing()
    
    try:
        # 选择呼吸效果模式
        print("选择呼吸灯模式:")
        print("1. 标准呼吸效果 (推荐)")
        print("2. 自定义呼吸效果")
        
        choice = input("请输入选择 (1 或 2，默认为1): ").strip()
        
        if choice == "2":
            led_controller.custom_breathing()
        else:
            led_controller.breathing_effect()
            
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        led_controller.cleanup()
        print("程序已退出")

if __name__ == "__main__":
    main()
