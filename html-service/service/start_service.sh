#!/bin/bash

# 树莓派电机控制服务启动脚本

echo "=== 树莓派L298N电机控制服务 ==="
echo "启动时间: $(date)"
echo

# 检查Python版本
echo "检查Python环境..."
python3 --version

# 检查并安装依赖
echo "检查依赖包..."
if [ -f "requirements.txt" ]; then
    echo "安装Python依赖包..."
    pip3 install -r requirements.txt
else
    echo "警告: requirements.txt 文件不存在"
fi

echo

# 检查GPIO权限
echo "检查GPIO权限..."
if groups $USER | grep -q gpio; then
    echo "✓ 用户已在gpio组中"
else
    echo "⚠ 警告: 用户不在gpio组中，可能需要sudo权限"
    echo "请运行: sudo usermod -a -G gpio $USER"
    echo "然后重新登录"
fi

echo

# 显示引脚配置
echo "L298N引脚配置:"
echo "  ENA (左电机使能) -> GPIO 11"
echo "  ENB (右电机使能) -> GPIO 12"
echo "  IN1 (左电机前进) -> GPIO 13"
echo "  IN2 (左电机后退) -> GPIO 15"
echo "  IN3 (右电机前进) -> GPIO 16"
echo "  IN4 (右电机后退) -> GPIO 18"
echo

# 启动服务
echo "启动电机控制服务..."
echo "服务地址: http://localhost:5000"
echo "按 Ctrl+C 停止服务"
echo "=========================="
echo

python3 motor_controller.py
