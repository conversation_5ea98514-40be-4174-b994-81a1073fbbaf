#!/bin/bash

# 树莓派电机控制系统 - 完整启动脚本
# 同时启动前端Web服务器和后端电机控制服务

echo "=========================================="
echo "🚀 树莓派电机控制系统启动脚本"
echo "=========================================="
echo "启动时间: $(date)"
echo

# 检查是否在正确的目录
if [ ! -d "html" ] || [ ! -d "service" ]; then
    echo "❌ 错误: 请在html-service目录中运行此脚本"
    echo "当前目录: $(pwd)"
    exit 1
fi

# 获取本机IP地址
get_ip() {
    # 尝试多种方法获取IP地址
    local ip=""
    
    # 方法1: 使用hostname命令
    ip=$(hostname -I | awk '{print $1}' 2>/dev/null)
    
    # 方法2: 使用ip命令
    if [ -z "$ip" ]; then
        ip=$(ip route get ******* | awk '{print $7; exit}' 2>/dev/null)
    fi
    
    # 方法3: 使用ifconfig命令
    if [ -z "$ip" ]; then
        ip=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -1)
    fi
    
    # 默认值
    if [ -z "$ip" ]; then
        ip="***********"  # 树莓派热点默认IP
    fi
    
    echo "$ip"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        return 0  # 端口被占用
    else
        return 1  # 端口空闲
    fi
}

# 停止已存在的服务
cleanup_services() {
    echo "🧹 清理已存在的服务..."
    
    # 停止可能运行的Python服务
    pkill -f "motor_controller.py" 2>/dev/null
    pkill -f "web_server.py" 2>/dev/null
    
    sleep 2
    echo "✅ 清理完成"
}

# 启动后端服务
start_backend() {
    echo "🔧 启动后端电机控制服务..."
    
    cd service
    
    # 检查依赖
    if [ -f "requirements.txt" ]; then
        echo "📦 检查Python依赖..."
        pip3 install -r requirements.txt --quiet
    fi
    
    # 检查端口5000
    if check_port 5000; then
        echo "⚠️  警告: 端口5000已被占用，尝试停止现有服务..."
        pkill -f "motor_controller.py" 2>/dev/null
        sleep 2
    fi
    
    # 启动后端服务
    echo "🚀 启动电机控制服务 (端口5000)..."
    python3 motor_controller.py &
    BACKEND_PID=$!
    
    cd ..
    
    # 等待后端服务启动
    echo "⏳ 等待后端服务启动..."
    sleep 3
    
    # 检查后端服务是否启动成功
    if kill -0 $BACKEND_PID 2>/dev/null; then
        echo "✅ 后端服务启动成功 (PID: $BACKEND_PID)"
    else
        echo "❌ 后端服务启动失败"
        exit 1
    fi
}

# 启动前端服务
start_frontend() {
    echo "🌐 启动前端Web服务器..."
    
    cd html
    
    # 检查端口8080
    if check_port 8080; then
        echo "⚠️  警告: 端口8080已被占用，尝试停止现有服务..."
        pkill -f "web_server.py" 2>/dev/null
        sleep 2
    fi
    
    # 启动前端服务
    echo "🚀 启动Web服务器 (端口8080)..."
    python3 web_server.py &
    FRONTEND_PID=$!
    
    cd ..
    
    # 等待前端服务启动
    sleep 2
    
    # 检查前端服务是否启动成功
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        echo "✅ 前端服务启动成功 (PID: $FRONTEND_PID)"
    else
        echo "❌ 前端服务启动失败"
        kill $BACKEND_PID 2>/dev/null
        exit 1
    fi
}

# 显示访问信息
show_access_info() {
    local ip=$(get_ip)

    echo
    echo "=========================================="
    echo "🎉 所有服务启动成功!"
    echo "=========================================="
    echo "📱 局域网访问地址:"
    echo "   http://$ip:8080"
    echo
    echo "🖥️  本地访问地址:"
    echo "   http://localhost:8080"
    echo
    echo "🔧 后端API地址:"
    echo "   http://$ip:5000"
    echo
    echo "📋 使用说明:"
    echo "1. 确保设备在同一网络下"
    echo "2. 在浏览器中访问: http://$ip:8080"
    echo "3. 使用界面控制电机"
    echo "4. 按 Ctrl+C 停止所有服务"
    echo
    echo "🔍 故障排除:"
    echo "   如无法访问，请检查防火墙设置"
    echo "   sudo ufw allow 8080"
    echo "   sudo ufw allow 5000"
    echo "=========================================="
}

# 信号处理函数
cleanup_and_exit() {
    echo
    echo "🛑 正在停止所有服务..."
    
    # 停止前端和后端服务
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
        echo "✅ 前端服务已停止"
    fi
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
        echo "✅ 后端服务已停止"
    fi
    
    # 额外清理
    pkill -f "motor_controller.py" 2>/dev/null
    pkill -f "web_server.py" 2>/dev/null
    
    echo "🏁 所有服务已停止"
    exit 0
}

# 注册信号处理
trap cleanup_and_exit SIGINT SIGTERM

# 主执行流程
main() {
    cleanup_services
    start_backend
    start_frontend
    show_access_info
    
    # 保持脚本运行，等待用户中断
    echo "⏳ 服务运行中... (按 Ctrl+C 停止)"
    while true; do
        sleep 1
        
        # 检查服务是否还在运行
        if ! kill -0 $BACKEND_PID 2>/dev/null; then
            echo "❌ 后端服务意外停止"
            cleanup_and_exit
        fi
        
        if ! kill -0 $FRONTEND_PID 2>/dev/null; then
            echo "❌ 前端服务意外停止"
            cleanup_and_exit
        fi
    done
}

# 运行主函数
main
