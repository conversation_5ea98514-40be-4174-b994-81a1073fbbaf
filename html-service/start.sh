#!/bin/bash

# 电机控制系统启动脚本
echo "🚗 启动电机控制系统..."
echo "================================"

# 检查Python3是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 检查pip3是否安装
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 未安装，请先安装pip3"
    exit 1
fi

# 安装依赖
echo "📦 检查并安装依赖..."
pip3 install flask flask-cors gpiozero

# 给Python脚本添加执行权限
chmod +x start_services.py

# 启动服务
echo "🚀 启动前后端服务..."
python3 start_services.py
