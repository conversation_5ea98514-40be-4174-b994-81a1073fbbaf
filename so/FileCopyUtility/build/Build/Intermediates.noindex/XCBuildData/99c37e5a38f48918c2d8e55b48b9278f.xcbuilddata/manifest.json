{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex": {"is-mutated": true}, "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release": {"is-mutated": true}, "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products": {"is-mutated": true}, "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release": {"is-mutated": true}, "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app": {"is-mutated": true}, "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS/FileCopyUtility": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS/FileCopyUtility normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>": {"is-command-timestamp": true}, "<TRIGGER: Validate /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/_CodeSignature", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "<target-FileCopyUtility-****************************************************************--begin-scanning>", "<target-FileCopyUtility-****************************************************************--end>", "<target-FileCopyUtility-****************************************************************--linker-inputs-ready>", "<target-FileCopyUtility-****************************************************************--modules-ready>", "<workspace-Release-macosx15.5-macos--stale-file-removal>"], "outputs": ["<all>"]}, "<target-FileCopyUtility-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS/FileCopyUtility", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/_CodeSignature", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/thinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/unthinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app.dSYM/Contents/Resources/DWARF/FileCopyUtility", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources/Assets.car", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_signature", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/thinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/unthinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Info.plist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/PkgInfo", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent.der", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility Swift Compilation Finished", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS/FileCopyUtility", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_lto.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_dependency_info.dat", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyViewModel.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtilityApp.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyViewModel.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtilityApp.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-master.swiftconstvalues", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftmodule", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftsourceinfo", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.abi.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-Swift.h", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftdoc", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/FileCopyUtility-Swift.h", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-all-target-headers.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-generated-files.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-own-target-headers.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-project-headers.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-OutputFileMap.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.LinkFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftConstValuesFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_const_extract_protocols.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/empty-FileCopyUtility.plist"], "roots": ["/tmp/FileCopyUtility.dst", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products"], "outputs": ["<target-FileCopyUtility-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>"]}, "<workspace-Release-macosx15.5-macos--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility-254c41a42a1e5cf406a5f0733b04dc2f-VFS/all-product-headers.yaml"], "outputs": ["<workspace-Release-macosx15.5-macos--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "<ClangStatCache /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility.xcodeproj", "signature": "0be614b3a5e34f8ca08bbefe9e014072"}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release"]}, "P0:::Gate /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app.dSYM-target-FileCopyUtility-****************************************************************-": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app.dSYM/Contents/Resources/DWARF/FileCopyUtility", "<GenerateDSYMFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app.dSYM/Contents/Resources/DWARF/FileCopyUtility>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app.dSYM/"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility-254c41a42a1e5cf406a5f0733b04dc2f-VFS/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftConstValuesFileList"], "outputs": ["<target-FileCopyUtility-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--Barrier-ChangePermissions>", "<target-FileCopyUtility-****************************************************************--will-sign>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--Barrier-StripSymbols>", "<target-FileCopyUtility-****************************************************************--will-sign>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-FileCopyUtility-****************************************************************--will-sign>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>"], "outputs": ["<target-FileCopyUtility-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--Barrier-GenerateStubAPI>", "<target-FileCopyUtility-****************************************************************--will-sign>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ProductPostprocessingTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--Barrier-CodeSign>", "<target-FileCopyUtility-****************************************************************--will-sign>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>"], "outputs": ["<target-FileCopyUtility-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--Barrier-Validate>", "<target-FileCopyUtility-****************************************************************--will-sign>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<LSRegisterURL /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>", "<Touch /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>"], "outputs": ["<target-FileCopyUtility-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--Barrier-CopyAside>", "<target-FileCopyUtility-****************************************************************--will-sign>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-FileCopyUtility-****************************************************************--will-sign>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>"], "outputs": ["<target-FileCopyUtility-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--CustomTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--GeneratedFilesTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ProductStructureTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent.der", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/Entitlements.plist"], "outputs": ["<target-FileCopyUtility-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-all-target-headers.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-generated-files.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-own-target-headers.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-project-headers.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.hmap"], "outputs": ["<target-FileCopyUtility-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Info.plist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/PkgInfo", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/empty-FileCopyUtility.plist"], "outputs": ["<target-FileCopyUtility-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--RealityAssetsTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-FileCopyUtility-****************************************************************--ModuleMapTaskProducer>", "<target-FileCopyUtility-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-FileCopyUtility-****************************************************************--InfoPlistTaskProducer>", "<target-FileCopyUtility-****************************************************************--SanitizerTaskProducer>", "<target-FileCopyUtility-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-FileCopyUtility-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-FileCopyUtility-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-FileCopyUtility-****************************************************************--TestTargetTaskProducer>", "<target-FileCopyUtility-****************************************************************--TestHostTaskProducer>", "<target-FileCopyUtility-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-FileCopyUtility-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-FileCopyUtility-****************************************************************--DocumentationTaskProducer>", "<target-FileCopyUtility-****************************************************************--CustomTaskProducer>", "<target-FileCopyUtility-****************************************************************--StubBinaryTaskProducer>", "<target-FileCopyUtility-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--start>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents>", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS>", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources>"], "outputs": ["<target-FileCopyUtility-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--HeadermapTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>"], "outputs": ["<target-FileCopyUtility-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ProductPostprocessingTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-FileCopyUtility-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources/Assets.car", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_signature", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/unthinned>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility Swift Compilation Finished", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_lto.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_dependency_info.dat", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyViewModel.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtilityApp.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyViewModel.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtilityApp.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-master.swiftconstvalues", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftmodule", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftsourceinfo", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.abi.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-Swift.h", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftdoc", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-OutputFileMap.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.LinkFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_const_extract_protocols.json"], "outputs": ["<target-FileCopyUtility-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-FileCopyUtility-****************************************************************--generated-headers>"]}, "P0:::Gate target-FileCopyUtility-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyViewModel.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtilityApp.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyViewModel.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtilityApp.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-master.swiftconstvalues", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftmodule", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftsourceinfo", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.abi.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-Swift.h", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftdoc", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/FileCopyUtility-Swift.h"], "outputs": ["<target-FileCopyUtility-****************************************************************--swift-generated-headers>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:CodeSign /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Assets.xcassets/", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/ContentView.swift/", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/FileCopyUtilityApp.swift/", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/FileCopyViewModel.swift/", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent/", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Info.plist/", "<target-FileCopyUtility-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-FileCopyUtility-****************************************************************--will-sign>", "<target-FileCopyUtility-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS/FileCopyUtility normal>", "<TRIGGER: MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/_CodeSignature", "<CodeSign /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>", "<TRIGGER: CodeSign /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:CompileAssetCatalogVariant thinned /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources /Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources /Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Assets.xcassets/", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/thinned>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/thinned", "<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_generated_info.plist_thinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Assets.xcassets", "--compile", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/thinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_generated_info.plist_thinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "14.0", "--platform", "macosx"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/so/FileCopyUtility", "control-enabled": false, "deps": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_dependencies_thinned"], "deps-style": "dependency-info", "signature": "af24fe7b5a62ef5f6b5127d40a9ea57c"}, "P0:target-FileCopyUtility-****************************************************************-:Release:CompileAssetCatalogVariant unthinned /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources /Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources /Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Assets.xcassets/", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/unthinned>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/unthinned", "<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_generated_info.plist_unthinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Assets.xcassets", "--compile", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/unthinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_generated_info.plist_unthinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "14.0", "--platform", "macosx"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/so/FileCopyUtility", "control-enabled": false, "deps": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_dependencies_unthinned"], "deps-style": "dependency-info", "signature": "64981b51c592ea21c54173f42dc47de8"}, "P0:target-FileCopyUtility-****************************************************************-:Release:CopySwiftLibs /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS/FileCopyUtility", "<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>"], "deps": "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-FileCopyUtility-****************************************************************-:Release:ExtractAppIntentsMetadata": {"tool": "shell", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/ContentView.swift", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/FileCopyViewModel.swift", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/FileCopyUtilityApp.swift", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-master.swiftconstvalues", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS/FileCopyUtility", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_dependency_info.dat", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftConstValuesFileList", "<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-FileCopyUtility-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources/Metadata.appintents>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor", "--toolchain-dir", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "--module-name", "FileCopyUtility", "--sdk-root", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "--xcode-version", "16F6", "--platform-family", "macOS", "--deployment-target", "14.0", "--bundle-identifier", "com.example.FileCopyUtility", "--output", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources", "--target-triple", "arm64-apple-macos14.0", "--binary-file", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS/FileCopyUtility", "--dependency-file", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_dependency_info.dat", "--stringsdata-file", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "--source-file-list", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftFileList", "--metadata-file-list", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.DependencyMetadataFileList", "--static-metadata-file-list", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.DependencyStaticMetadataFileList", "--swift-const-vals-list", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftConstValuesFileList", "--compile-time-extraction", "--deployment-aware-processing", "--validate-assistant-intents", "--no-app-shortcuts-localization"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/so/FileCopyUtility", "signature": "c27c1d4f2a024fcbdc67a92b38abde2a"}, "P0:target-FileCopyUtility-****************************************************************-:Release:Gate target-FileCopyUtility-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/FileCopyUtility.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release>"], "outputs": ["<target-FileCopyUtility-****************************************************************--begin-compiling>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:Gate target-FileCopyUtility-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/FileCopyUtility.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release>"], "outputs": ["<target-FileCopyUtility-****************************************************************--begin-linking>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:Gate target-FileCopyUtility-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/FileCopyUtility.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--begin-scanning>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:Gate target-FileCopyUtility-****************************************************************--end": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--entry>", "<CodeSign /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "<GenerateDSYMFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app.dSYM/Contents/Resources/DWARF/FileCopyUtility>", "<GenerateDSYMFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app.dSYM/Contents/Resources/DWARF/FileCopyUtility>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources/Assets.car", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_signature", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/unthinned>", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents>", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS>", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Info.plist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/PkgInfo", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>", "<LSRegisterURL /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility Swift Compilation Finished", "<Touch /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>", "<Validate /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>", "<ValidateDevelopmentAssets-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_lto.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_dependency_info.dat", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyViewModel.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtilityApp.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyViewModel.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtilityApp.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-master.swiftconstvalues", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftmodule", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftsourceinfo", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.abi.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-Swift.h", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftdoc", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/FileCopyUtility-Swift.h", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/FileCopyUtility-Swift.h", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-all-target-headers.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-generated-files.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-own-target-headers.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-project-headers.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-OutputFileMap.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.LinkFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftConstValuesFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_const_extract_protocols.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/empty-FileCopyUtility.plist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app.dSYM/", "<target-FileCopyUtility-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-FileCopyUtility-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-FileCopyUtility-****************************************************************--Barrier-ChangePermissions>", "<target-FileCopyUtility-****************************************************************--Barrier-CodeSign>", "<target-FileCopyUtility-****************************************************************--Barrier-CopyAside>", "<target-FileCopyUtility-****************************************************************--Barrier-GenerateStubAPI>", "<target-FileCopyUtility-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-FileCopyUtility-****************************************************************--Barrier-RegisterProduct>", "<target-FileCopyUtility-****************************************************************--Barrier-StripSymbols>", "<target-FileCopyUtility-****************************************************************--Barrier-Validate>", "<target-FileCopyUtility-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-FileCopyUtility-****************************************************************--CustomTaskProducer>", "<target-FileCopyUtility-****************************************************************--DocumentationTaskProducer>", "<target-FileCopyUtility-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-FileCopyUtility-****************************************************************--GeneratedFilesTaskProducer>", "<target-FileCopyUtility-****************************************************************--HeadermapTaskProducer>", "<target-FileCopyUtility-****************************************************************--InfoPlistTaskProducer>", "<target-FileCopyUtility-****************************************************************--ModuleMapTaskProducer>", "<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--ProductPostprocessingTaskProducer>", "<target-FileCopyUtility-****************************************************************--ProductStructureTaskProducer>", "<target-FileCopyUtility-****************************************************************--RealityAssetsTaskProducer>", "<target-FileCopyUtility-****************************************************************--SanitizerTaskProducer>", "<target-FileCopyUtility-****************************************************************--StubBinaryTaskProducer>", "<target-FileCopyUtility-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-FileCopyUtility-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-FileCopyUtility-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-FileCopyUtility-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-FileCopyUtility-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-FileCopyUtility-****************************************************************--TestHostTaskProducer>", "<target-FileCopyUtility-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-FileCopyUtility-****************************************************************--TestTargetTaskProducer>", "<target-FileCopyUtility-****************************************************************--copy-headers-completion>", "<target-FileCopyUtility-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-FileCopyUtility-****************************************************************--generated-headers>", "<target-FileCopyUtility-****************************************************************--swift-generated-headers>"], "outputs": ["<target-FileCopyUtility-****************************************************************--end>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:Gate target-FileCopyUtility-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/FileCopyUtility.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release>", "<target-FileCopyUtility-****************************************************************--begin-compiling>"], "outputs": ["<target-FileCopyUtility-****************************************************************--entry>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:Gate target-FileCopyUtility-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/FileCopyUtility.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release>"], "outputs": ["<target-FileCopyUtility-****************************************************************--immediate>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:Gate target-FileCopyUtility-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_lto.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_dependency_info.dat", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyViewModel.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtilityApp.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyViewModel.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtilityApp.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-master.swiftconstvalues", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftmodule", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftsourceinfo", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.abi.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-Swift.h", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftdoc", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.LinkFileList"], "outputs": ["<target-FileCopyUtility-****************************************************************--linker-inputs-ready>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:Gate target-FileCopyUtility-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyViewModel.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtilityApp.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyViewModel.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtilityApp.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-master.swiftconstvalues", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftmodule", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftsourceinfo", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.abi.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-Swift.h", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftdoc", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/FileCopyUtility-Swift.h"], "outputs": ["<target-FileCopyUtility-****************************************************************--modules-ready>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:Gate target-FileCopyUtility-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "<GenerateDSYMFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app.dSYM/Contents/Resources/DWARF/FileCopyUtility>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources/Assets.car", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_signature", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/unthinned>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent.der", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility Swift Compilation Finished", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_lto.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_dependency_info.dat", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyViewModel.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtilityApp.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyViewModel.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtilityApp.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-master.swiftconstvalues", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftmodule", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftsourceinfo", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.abi.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-Swift.h", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftdoc", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/FileCopyUtility-Swift.h", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.DependencyMetadataFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-OutputFileMap.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.LinkFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftConstValuesFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_const_extract_protocols.json", "<target-FileCopyUtility-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-FileCopyUtility-****************************************************************--unsigned-product-ready>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:Gate target-FileCopyUtility-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-FileCopyUtility-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-FileCopyUtility-****************************************************************--will-sign>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:GenerateAssetSymbols /Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Assets.xcassets/", "<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Assets.xcassets", "--compile", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "14.0", "--platform", "macosx", "--bundle-identifier", "com.example.FileCopyUtility", "--generate-swift-asset-symbols", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/so/FileCopyUtility", "control-enabled": false, "signature": "fe3ad4dad14339fd76f3592ceb906e53"}, "P0:target-FileCopyUtility-****************************************************************-:Release:GenerateDSYMFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app.dSYM /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS/FileCopyUtility": {"tool": "shell", "description": "GenerateDSYMFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app.dSYM /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS/FileCopyUtility", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS/FileCopyUtility", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Info.plist", "<Linked Binary /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS/FileCopyUtility>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftmodule", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.swiftmodule", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app.dSYM/Contents/Resources/DWARF/FileCopyUtility", "<GenerateDSYMFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app.dSYM/Contents/Resources/DWARF/FileCopyUtility>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/dsymutil", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS/FileCopyUtility", "-o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app.dSYM"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/so/FileCopyUtility", "signature": "93b455524536d1ea26a733717278b059"}, "P0:target-FileCopyUtility-****************************************************************-:Release:LinkAssetCatalog /Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Assets.xcassets/", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_signature", "<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources/Assets.car"], "deps": "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_dependencies"}, "P0:target-FileCopyUtility-****************************************************************-:Release:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-FileCopyUtility-****************************************************************-:Release:MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/thinned", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/thinned>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/unthinned", "inputs": ["<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_output/unthinned>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app", "inputs": ["<target-FileCopyUtility-****************************************************************--start>", "<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>", "<TRIGGER: MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents", "inputs": ["<target-FileCopyUtility-****************************************************************--start>", "<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS", "inputs": ["<target-FileCopyUtility-****************************************************************--start>", "<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources", "inputs": ["<target-FileCopyUtility-****************************************************************--start>", "<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources", "<MkDir /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Resources>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:ProcessInfoPlistFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Info.plist /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/empty-FileCopyUtility.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Info.plist /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/empty-FileCopyUtility.plist", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/empty-FileCopyUtility.plist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/assetcatalog_generated_info.plist", "<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Info.plist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/PkgInfo"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:ProcessProductPackaging /Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/FileCopyUtility.entitlements /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging /Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/FileCopyUtility.entitlements /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/FileCopyUtility.entitlements", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/Entitlements.plist", "<target-FileCopyUtility-****************************************************************--ProductStructureTaskProducer>", "<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:ProcessProductPackagingDER /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent.der", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent", "<target-FileCopyUtility-****************************************************************--ProductStructureTaskProducer>", "<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent", "-o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.app.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/so/FileCopyUtility", "signature": "2bb6f22cdb0eefc2452c3fae8c907f7d"}, "P0:target-FileCopyUtility-****************************************************************-:Release:RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app", "<target-FileCopyUtility-****************************************************************--Barrier-CodeSign>", "<target-FileCopyUtility-****************************************************************--will-sign>", "<target-FileCopyUtility-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:RegisterWithLaunchServices /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app": {"tool": "lsregisterurl", "description": "RegisterWithLaunchServices /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app", "inputs": ["<target-FileCopyUtility-****************************************************************--Barrier-Validate>", "<target-FileCopyUtility-****************************************************************--will-sign>", "<target-FileCopyUtility-****************************************************************--entry>", "<TRIGGER: Validate /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>"], "outputs": ["<LSRegisterURL /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:SwiftDriver Compilation FileCopyUtility normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation FileCopyUtility normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/ContentView.swift", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/FileCopyViewModel.swift", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/FileCopyUtilityApp.swift", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-OutputFileMap.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_const_extract_protocols.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-generated-files.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-own-target-headers.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-all-target-headers.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-project-headers.hmap", "<ClangStatCache /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-FileCopyUtility-****************************************************************--copy-headers-completion>", "<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility Swift Compilation Finished"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:Touch /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app": {"tool": "shell", "description": "Touch /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app", "<target-FileCopyUtility-****************************************************************--Barrier-Validate>", "<target-FileCopyUtility-****************************************************************--will-sign>", "<target-FileCopyUtility-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/so/FileCopyUtility", "signature": "f3611c4a0f01693657d49571026b0c4b"}, "P0:target-FileCopyUtility-****************************************************************-:Release:Validate /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/Info.plist", "<target-FileCopyUtility-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-FileCopyUtility-****************************************************************--will-sign>", "<target-FileCopyUtility-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>"], "outputs": ["<Validate /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>", "<TRIGGER: Validate /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app>"]}, "P0:target-FileCopyUtility-****************************************************************-:Release:ValidateDevelopmentAssets /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build": {"tool": "validate-development-assets", "description": "ValidateDevelopmentAssets /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/Preview Content", "<target-FileCopyUtility-****************************************************************--entry>"], "outputs": ["<ValidateDevelopmentAssets-/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build>"], "allow-missing-inputs": true}, "P2:::WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility-254c41a42a1e5cf406a5f0733b04dc2f-VFS/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility-254c41a42a1e5cf406a5f0733b04dc2f-VFS/all-product-headers.yaml", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility-254c41a42a1e5cf406a5f0733b04dc2f-VFS/all-product-headers.yaml"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:Copy /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftsourceinfo", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftsourceinfo/", "<target-FileCopyUtility-****************************************************************--copy-headers-completion>", "<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:Copy /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.abi.json /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.abi.json /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.abi.json", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.abi.json/", "<target-FileCopyUtility-****************************************************************--copy-headers-completion>", "<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.abi.json"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:Copy /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.swiftdoc /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.swiftdoc /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftdoc", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftdoc/", "<target-FileCopyUtility-****************************************************************--copy-headers-completion>", "<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.swiftdoc"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:Copy /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.swiftmodule /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.swiftmodule /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftmodule", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftmodule/", "<target-FileCopyUtility-****************************************************************--copy-headers-completion>", "<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.swiftmodule/arm64-apple-macos.swiftmodule"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:Ld /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS/FileCopyUtility normal": {"tool": "shell", "description": "Ld /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS/FileCopyUtility normal", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyViewModel.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtilityApp.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.LinkFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release", "<target-FileCopyUtility-****************************************************************--generated-headers>", "<target-FileCopyUtility-****************************************************************--swift-generated-headers>", "<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS/FileCopyUtility", "<Linked Binary /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS/FileCopyUtility>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_lto.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS/FileCopyUtility normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-macos14.0", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-<PERSON><PERSON>", "-L/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release", "-L/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release", "-F/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release", "-F/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release", "-filelist", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.LinkFileList", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/../Frameworks", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_lto.o", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftmodule", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Products/Release/FileCopyUtility.app/Contents/MacOS/FileCopyUtility"], "env": {}, "working-directory": "/Users/<USER>/Desktop/]ig/so/FileCopyUtility", "deps": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_dependency_info.dat"], "deps-style": "dependency-info", "signature": "35a6dabb7d2cddcb52f941ca02fa6956"}, "P2:target-FileCopyUtility-****************************************************************-:Release:SwiftDriver Compilation Requirements FileCopyUtility normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements FileCopyUtility normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/ContentView.swift", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/FileCopyViewModel.swift", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/FileCopyUtility/FileCopyUtilityApp.swift", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftFileList", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-OutputFileMap.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_const_extract_protocols.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-generated-files.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-own-target-headers.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-all-target-headers.hmap", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-project-headers.hmap", "<ClangStatCache /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-FileCopyUtility-****************************************************************--copy-headers-completion>", "<target-FileCopyUtility-****************************************************************--ModuleVerifierTaskProducer>", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyViewModel.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtilityApp.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyViewModel.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtilityApp.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-master.swiftconstvalues", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftmodule", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftsourceinfo", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.abi.json", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-Swift.h", "/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.swiftdoc"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/FileCopyUtility-Swift.h /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/FileCopyUtility-Swift.h /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-Swift.h", "inputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-Swift.h", "<target-FileCopyUtility-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/FileCopyUtility-Swift.h"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/Entitlements.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/Entitlements.plist", "inputs": ["<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/DerivedSources/Entitlements.plist"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-all-non-framework-target-headers.hmap", "inputs": ["<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-all-non-framework-target-headers.hmap"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-all-target-headers.hmap", "inputs": ["<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-all-target-headers.hmap"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-generated-files.hmap", "inputs": ["<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-generated-files.hmap"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-own-target-headers.hmap", "inputs": ["<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-own-target-headers.hmap"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-project-headers.hmap", "inputs": ["<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility-project-headers.hmap"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.DependencyMetadataFileList", "inputs": ["<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.DependencyMetadataFileList"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.DependencyStaticMetadataFileList", "inputs": ["<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.DependencyStaticMetadataFileList"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.hmap", "inputs": ["<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/FileCopyUtility.hmap"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-OutputFileMap.json", "inputs": ["<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility-OutputFileMap.json"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.LinkFileList", "inputs": ["<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.LinkFileList"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftConstValuesFileList", "inputs": ["<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftConstValuesFileList"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftFileList", "inputs": ["<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility.SwiftFileList"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_const_extract_protocols.json", "inputs": ["<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/Objects-normal/arm64/FileCopyUtility_const_extract_protocols.json"]}, "P2:target-FileCopyUtility-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/empty-FileCopyUtility.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/empty-FileCopyUtility.plist", "inputs": ["<target-FileCopyUtility-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/]ig/so/FileCopyUtility/build/Build/Intermediates.noindex/FileCopyUtility.build/Release/FileCopyUtility.build/empty-FileCopyUtility.plist"]}}}