<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>8FB2B15D-392B-4D02-B384-8A775C8B6153</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>8FB2B15D-392B-4D02-B384-8A775C8B6153.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>S</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>FileCopyUtility project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>FileCopyUtility</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Cleaning project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>timeStartedRecording</key>
			<real>777972573.12826002</real>
			<key>timeStoppedRecording</key>
			<real>777972573.35553896</real>
			<key>title</key>
			<string>Cleaning project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>uniqueIdentifier</key>
			<string>8FB2B15D-392B-4D02-B384-8A775C8B6153</string>
		</dict>
		<key>AB4CED8A-051E-4BCE-B7B4-0A4D8484B23A</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>AB4CED8A-051E-4BCE-B7B4-0A4D8484B23A.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>W</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>6</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>FileCopyUtility project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>FileCopyUtility</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Building project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>timeStartedRecording</key>
			<real>777972573.35782301</real>
			<key>timeStoppedRecording</key>
			<real>777972578.73554897</real>
			<key>title</key>
			<string>Building project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>uniqueIdentifier</key>
			<string>AB4CED8A-051E-4BCE-B7B4-0A4D8484B23A</string>
		</dict>
		<key>D32E24C6-1B09-444A-918A-26BFCC7A52F7</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>D32E24C6-1B09-444A-918A-26BFCC7A52F7.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>W</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>6</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>FileCopyUtility project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>FileCopyUtility</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Building project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>timeStartedRecording</key>
			<real>777972016.21714795</real>
			<key>timeStoppedRecording</key>
			<real>777972021.11525202</real>
			<key>title</key>
			<string>Building project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>uniqueIdentifier</key>
			<string>D32E24C6-1B09-444A-918A-26BFCC7A52F7</string>
		</dict>
	</dict>
</dict>
</plist>
