import SwiftUI

// 全局会话过滤管理器
class ConversationFilterManager: ObservableObject {
    static let shared = ConversationFilterManager()

    private init() {
        setupGlobalNotificationObservers()
    }

    deinit {
        removeGlobalNotificationObservers()
    }

    private var globalNotificationObservers: [NSObjectProtocol] = []

    // 设置全局通知观察者
    private func setupGlobalNotificationObservers() {
        // 监听新消息通知 - 只处理删除会话的恢复逻辑
        let newMessageObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name("NewMessageReceived"),
            object: nil,
            queue: .main
        ) { [weak self] notification in
            if let userInfo = notification.userInfo,
               let conversationId = userInfo["conversation_id"] as? String {
                // 只处理删除会话的恢复逻辑
                self?.handleNewMessage(conversationId: conversationId, userInfo: userInfo)
            }
        }
        globalNotificationObservers.append(newMessageObserver)
    }

    private func removeGlobalNotificationObservers() {
        for observer in globalNotificationObservers {
            NotificationCenter.default.removeObserver(observer)
        }
        globalNotificationObservers.removeAll()
    }

    // 处理新消息，检查是否需要从过滤列表中移除会话
    private func handleNewMessage(conversationId: String, userInfo: [AnyHashable: Any]) {
        // 获取当前用户ID（从UserState获取）
        let currentUserId = UserState.shared.userId

        // 检查会话是否在删除列表中
        if isConversationDeleted(conversationId: conversationId, userId: currentUserId) {

            // 从过滤列表中移除
            removeFromDeletedList(conversationId: conversationId, userId: currentUserId)

            // 通知会话列表页面强制重新加载（而不是智能更新）
            // 因为被删除的会话可能不在当前的conversations数组中
            NotificationCenter.default.post(
                name: NSNotification.Name("ConversationFilterUpdated"),
                object: nil,
                userInfo: [
                    "conversation_id": conversationId,
                    "action": "restored",
                    "force_reload": true,  // 标记需要强制重新加载
                    "user_id": currentUserId  // 传递用户ID，用于同步本地变量
                ]
            )
        }
    }

    // 检查会话是否在删除列表中
    func isConversationDeleted(conversationId: String, userId: Int) -> Bool {
        do {
            let fileManager = FileManager.default
            let documentsDirectory = try fileManager.url(for: .documentDirectory, in: .userDomainMask, appropriateFor: nil, create: false)
            let fileURL = documentsDirectory.appendingPathComponent("deleted_conversations_\(userId).json")

            if fileManager.fileExists(atPath: fileURL.path) {
                let data = try Data(contentsOf: fileURL)
                let deletedIds = try JSONDecoder().decode([String].self, from: data)
                return deletedIds.contains(conversationId)
            }
        } catch {
            print("检查删除列表失败: \(error)")
        }
        return false
    }

    // 从删除列表中移除会话
    func removeFromDeletedList(conversationId: String, userId: Int) {
        do {
            let fileManager = FileManager.default
            let documentsDirectory = try fileManager.url(for: .documentDirectory, in: .userDomainMask, appropriateFor: nil, create: false)
            let fileURL = documentsDirectory.appendingPathComponent("deleted_conversations_\(userId).json")

            var deletedIds: [String] = []
            if fileManager.fileExists(atPath: fileURL.path) {
                let data = try Data(contentsOf: fileURL)
                deletedIds = try JSONDecoder().decode([String].self, from: data)
            }

            // 移除指定的会话ID
            deletedIds.removeAll { $0 == conversationId }

            // 保存更新后的列表
            let data = try JSONEncoder().encode(deletedIds)
            try data.write(to: fileURL)

        } catch {
            print("从删除列表中移除会话失败: \(error)")
        }
    }

    // 添加会话到删除列表
    func addToDeletedList(conversationId: String, userId: Int) {
        do {
            let fileManager = FileManager.default
            let documentsDirectory = try fileManager.url(for: .documentDirectory, in: .userDomainMask, appropriateFor: nil, create: false)
            let fileURL = documentsDirectory.appendingPathComponent("deleted_conversations_\(userId).json")

            var deletedIds: [String] = []
            if fileManager.fileExists(atPath: fileURL.path) {
                let data = try Data(contentsOf: fileURL)
                deletedIds = try JSONDecoder().decode([String].self, from: data)
            }

            // 添加新的会话ID（如果不存在）
            if !deletedIds.contains(conversationId) {
                deletedIds.append(conversationId)

                // 保存更新后的列表
                let data = try JSONEncoder().encode(deletedIds)
                try data.write(to: fileURL)

            }
        } catch {
            print("添加会话到删除列表失败: \(error)")
        }
    }
}

// 会话列表数据模型
struct ConversationListData: Codable {
    let conversations: [Conversation]
    let total: Int
}

// 会话模型
struct Conversation: Identifiable, Codable, Equatable {
    let conversation_id: String
    let user_id: Int
    let username: String
    let avatar_url: String
    var latest_message: String
    var latest_message_time: Date
    var unread_count: Int
    var is_muted: Bool

    var id: String { conversation_id }

    // 格式化时间
    var formattedTime: String {
        // 如果是默认时间（1970年），显示空字符串
        if latest_message_time.timeIntervalSince1970 < 100 {
            return ""
        }

        let now = Date()
        let calendar = Calendar.current
        let formatter = DateFormatter()

        // 判断是否为今天
        if calendar.isDate(latest_message_time, inSameDayAs: now) {
            // 当天：只显示时分 (HH:mm)
            formatter.dateFormat = "HH:mm"
        } else if calendar.isDate(latest_message_time, equalTo: now, toGranularity: .year) {
            // 本年但非今天：显示月日 (MM/dd)
            formatter.dateFormat = "MM/dd"
        } else {
            // 非本年：显示年月日 (yyyy/MM/dd)
            formatter.dateFormat = "yyyy/MM/dd"
        }

        return formatter.string(from: latest_message_time)
    }

    // 普通初始化器
    init(conversation_id: String, user_id: Int, username: String, avatar_url: String, latest_message: String, latest_message_time: Date, unread_count: Int, is_muted: Bool = false) {
        self.conversation_id = conversation_id
        self.user_id = user_id
        self.username = username
        self.avatar_url = avatar_url
        self.latest_message = latest_message
        self.latest_message_time = latest_message_time
        self.unread_count = unread_count
        self.is_muted = is_muted
    }

    // 自定义解码
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        conversation_id = try container.decode(String.self, forKey: .conversation_id)
        user_id = try container.decode(Int.self, forKey: .user_id)
        username = try container.decode(String.self, forKey: .username)
        avatar_url = try container.decode(String.self, forKey: .avatar_url)
        latest_message = try container.decode(String.self, forKey: .latest_message)
        unread_count = try container.decode(Int.self, forKey: .unread_count)
        is_muted = try container.decodeIfPresent(Bool.self, forKey: .is_muted) ?? false

        // 尝试解码时间，如果失败则使用默认时间
        if let timeString = try? container.decode(String.self, forKey: .latest_message_time) {

            // 尝试多种时间格式
            var parsedDate: Date?

            // 1. 检查是否带有时区信息（Z结尾）
            if timeString.hasSuffix("Z") {
                // 带时区的 ISO8601 格式
                let iso8601Formatter = ISO8601DateFormatter()
                iso8601Formatter.formatOptions = [.withInternetDateTime]
                parsedDate = iso8601Formatter.date(from: timeString)
            } else {
                // 2. 不带时区的格式，假设是北京时间
                let customFormatter = DateFormatter()
                customFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
                customFormatter.timeZone = TimeZone(identifier: "Asia/Shanghai") // 北京时间
                parsedDate = customFormatter.date(from: timeString)

                if parsedDate == nil {
                    // 3. 尝试数据库格式：yyyy-MM-dd HH:mm:ss
                    customFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
                    customFormatter.timeZone = TimeZone(identifier: "Asia/Shanghai") // 北京时间
                    parsedDate = customFormatter.date(from: timeString)
                }

                if parsedDate == nil {
                    // 4. 尝试当前时区格式
                    customFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
                    customFormatter.timeZone = TimeZone.current
                    parsedDate = customFormatter.date(from: timeString)
                }
            }

            if let date = parsedDate {
                latest_message_time = date
            } else {
                latest_message_time = Date(timeIntervalSince1970: 0)
            }
        } else {
            latest_message_time = Date(timeIntervalSince1970: 0)
        }
    }

    // 编码键
    private enum CodingKeys: String, CodingKey {
        case conversation_id, user_id, username, avatar_url, latest_message, latest_message_time, unread_count, is_muted
    }

    static func == (lhs: Conversation, rhs: Conversation) -> Bool {
        return lhs.conversation_id == rhs.conversation_id
    }
}

// 会话行视图
struct ConversationRow: View {
    let conversation: Conversation
    
    var body: some View {
        HStack(spacing: 12) {
            // 头像 - 使用本地头像或默认头像
            if conversation.avatar_url.isEmpty {
                // 默认头像
                Circle()
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 50, height: 50)
                    .overlay(
                        Text(String(conversation.username.prefix(1)))
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(.gray)
                    )
            } else if conversation.avatar_url.contains("http") {
                // 远程头像URL
                AsyncImage(url: URL(string: conversation.avatar_url)) { phase in
                    if let image = phase.image {
                        image
                            .resizable()
                            .scaledToFill()
                    } else {
                        Circle()
                            .fill(Color.gray.opacity(0.3))
                    }
                }
                .frame(width: 50, height: 50)
                .clipShape(Circle())
            } else {
                // 本地头像名称
                Image(conversation.avatar_url)
                    .resizable()
                    .scaledToFill()
                    .frame(width: 50, height: 50)
                    .clipShape(Circle())
            }
            
            // 用户名、消息内容和时间
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(conversation.username)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.black)
                    
                    Spacer()
                    
                    Text(conversation.formattedTime)
                        .font(.system(size: 12))
                        .foregroundColor(.gray)
                }
                
                HStack {
                    Text(conversation.latest_message)
                        .font(.system(size: 14))
                        .foregroundColor(.gray)
                        .lineLimit(1)

                    Spacer()

                    if conversation.is_muted {
                        // 显示免打扰图标
                        Image(systemName: "bell.slash.fill")
                            .font(.system(size: 14))
                            .foregroundColor(.gray)
                    } else if conversation.unread_count > 0 {
                        // 显示未读消息数量
                        Text("\(conversation.unread_count)")
                            .font(.system(size: 12))
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.red)
                            .clipShape(Capsule())
                    }
                }
            }
        }
        .padding(.vertical, 8)
        .contentShape(Rectangle())
    }
}

class ConversationListViewModel: ObservableObject {
    @Published var conversations: [Conversation] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var refreshTrigger = false // 用于强制UI刷新

    let userId: Int
    private var isRequestInProgress = false

    // 存储被删除的会话ID列表
    var deletedConversationIds: Set<String> = []

    // 防抖机制：避免短时间内多次调用
    private var lastLoadTime: Date = Date.distantPast
    private let loadDebounceInterval: TimeInterval = 1.0 // 1秒内只允许一次加载

    // 从聊天页返回的防抖机制
    private var lastChatReturnTime: Date = Date.distantPast
    private let chatReturnDebounceInterval: TimeInterval = 0.5 // 0.5秒内只允许一次从聊天页返回的刷新

    // 通知观察者管理
    private var notificationObservers: [NSObjectProtocol] = []
    
    init(userId: Int) {
        self.userId = userId
        loadDeletedConversationIds()
        loadConversationsFromCache()
        // 立即设置通知观察者，确保即使不在会话列表页也能处理消息
        setupNotificationObservers()
        loadConversations()
    }

    deinit {
        removeNotificationObservers()
    }

    func setupNotificationObservers() {
        // 先移除所有现有观察者
        removeNotificationObservers()

        // 注册新消息通知观察者
        let newMessageObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name("NewMessageReceived"),
            object: nil,
            queue: .main
        ) { [weak self] notification in
            if let userInfo = notification.userInfo,
               let conversationId = userInfo["conversation_id"] as? String {
                let messageData = userInfo.compactMapValues { $0 }.reduce(into: [String: Any]()) { result, element in
                    if let key = element.key as? String {
                        result[key] = element.value
                    }
                }
                self?.handleNewMessage(messageData, conversationId: conversationId)
            } else {
                self?.loadConversations()
            }
        }
        notificationObservers.append(newMessageObserver)

        // 注册聊天页面消失通知观察者
        let chatDisappearObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name("ChatViewDidDisappear"),
            object: nil,
            queue: .main
        ) { [weak self] notification in
            if let userInfo = notification.userInfo,
               let conversationId = userInfo["conversation_id"] as? String {
                self?.loadConversationsFromChatReturn(conversationId: conversationId)
            }
        }
        notificationObservers.append(chatDisappearObserver)

        // 注册聊天记录清空通知观察者
        let historyClearedObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name("ConversationHistoryCleared"),
            object: nil,
            queue: .main
        ) { [weak self] notification in
            self?.loadConversations()
        }
        notificationObservers.append(historyClearedObserver)

        // 注册会话过滤更新通知观察者
        let filterUpdatedObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name("ConversationFilterUpdated"),
            object: nil,
            queue: .main
        ) { [weak self] notification in
            if let userInfo = notification.userInfo,
               let conversationId = userInfo["conversation_id"] as? String,
               let action = userInfo["action"] as? String {
                if action == "restored" {
                    // 同步更新本地的删除列表
                    if let notificationUserId = userInfo["user_id"] as? Int,
                       notificationUserId == self?.userId {
                        self?.deletedConversationIds.remove(conversationId)
                    }

                    let forceReload = userInfo["force_reload"] as? Bool ?? false

                    if forceReload {
                        // 强制重新加载会话列表（用于被删除的会话有新消息时）
                        self?.forceLoadConversations()
                    } else {
                        // 普通重新加载
                        self?.loadConversations()
                    }
                }
            }
        }
        notificationObservers.append(filterUpdatedObserver)
    }

    func removeNotificationObservers() {
        for observer in notificationObservers {
            NotificationCenter.default.removeObserver(observer)
        }
        notificationObservers.removeAll()
    }

    // 强制刷新UI
    private func forceRefreshUI() {
        DispatchQueue.main.async { [weak self] in
            self?.refreshTrigger.toggle()
            self?.objectWillChange.send()
        }
    }

    // 检查并恢复有未读消息的被删除会话
    private func checkAndRestoreConversationsWithUnreadMessages(_ conversations: [Conversation]) {
        var conversationsToRestore: [String] = []

        for conversation in conversations {
            // 检查会话是否在删除列表中且有未读消息
            if deletedConversationIds.contains(conversation.conversation_id) && conversation.unread_count > 0 {
                conversationsToRestore.append(conversation.conversation_id)
            }
        }

        // 恢复有未读消息的会话
        for conversationId in conversationsToRestore {
            // 从本地删除列表中移除
            deletedConversationIds.remove(conversationId)

            // 使用全局管理器从文件中移除
            ConversationFilterManager.shared.removeFromDeletedList(conversationId: conversationId, userId: userId)
        }

        // 如果有会话被恢复，保存更新后的删除列表
        if !conversationsToRestore.isEmpty {
            saveDeletedConversationIds()
        }
    }
    
    func loadConversations() {
        // 防止重复请求
        if isRequestInProgress {
            return
        }

        // 防抖机制：避免短时间内多次调用
        let now = Date()
        if now.timeIntervalSince(lastLoadTime) < loadDebounceInterval {
            return
        }
        lastLoadTime = now

        isLoading = true
        isRequestInProgress = true
        errorMessage = nil

        guard let url = URL(string: "https://sorealhuman.com:8888/messages/conversations") else {
            isLoading = false
            isRequestInProgress = false
            errorMessage = "无效的URL"
            return
        }

        // 获取所有会话的已读消息ID
        let readMessageIds = LastMessageIDManager.shared.getAllReadMessageIDs()

        // 构建请求体
        let requestBody: [String: Any] = [
            "user_id": userId,
            "read_message_ids": readMessageIds,
            "skip": 0,
            "limit": 20
        ]

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        } catch {
            isLoading = false
            isRequestInProgress = false
            errorMessage = "请求数据编码失败"
            return
        }

        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            DispatchQueue.main.async {
                // 确保在所有情况下都重置请求状态
                defer {
                    self?.isLoading = false
                    self?.isRequestInProgress = false
                }

                if let error = error {
                    self?.errorMessage = "加载失败: \(error.localizedDescription)"
                    return
                }

                guard let httpResponse = response as? HTTPURLResponse else {
                    self?.errorMessage = "无效的响应"
                    return
                }

                if httpResponse.statusCode == 404 {
                    // 用户没有会话，这是正常的，不显示错误
                    // 确保会话列表为空
                    self?.conversations = []
                    self?.saveConversationsToCache()
                    return
                }

                if httpResponse.statusCode != 200 {
                    self?.errorMessage = "服务器错误: \(httpResponse.statusCode)"
                    return
                }
                
                guard let data = data else {
                    self?.errorMessage = "没有数据"
                    return
                }
                
                do {
                    // 优先尝试使用标准解码器
                    let decoder = JSONDecoder()
                    decoder.dateDecodingStrategy = .iso8601

                    let response = try decoder.decode(APIResponse<ConversationListData>.self, from: data)
                    if response.success {
                        let allConversations = response.data?.conversations ?? []
                        // 检查删除列表中的会话是否有未读消息，如果有则恢复
                        self?.checkAndRestoreConversationsWithUnreadMessages(allConversations)

                        let filteredConversations = self?.filterConversations(allConversations) ?? []

                        // 确保在主线程更新UI
                        DispatchQueue.main.async {
                            self?.conversations = filteredConversations
                            self?.saveConversationsToCache()

                            // 更新本地免打扰状态
                            ConversationMuteManager.shared.updateMuteStates(from: filteredConversations)

                            // 更新全局未读消息计数
                            self?.updateGlobalUnreadCount()

                            self?.forceRefreshUI()
                        }
                        return
                    } else {
                        self?.errorMessage = response.message ?? "获取会话列表失败"
                        return
                    }
                } catch {
                    // 如果标准解码失败，尝试手动解析
                    do {
                        if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        // 检查是否有success字段
                        if let success = json["success"] as? Bool, success {
                            // 检查是否有data字段
                            if let responseData = json["data"] as? [String: Any] {
                                // 尝试获取conversations数组
                                if let conversationsArray = responseData["conversations"] as? [[String: Any]] {
                                    // 有会话数据，解析它们
                                    var parsedConversations: [Conversation] = []
                                    
                                    for conversationData in conversationsArray {
                                        if let conversationId = conversationData["conversation_id"] as? String,
                                           let userId = conversationData["user_id"] as? Int,
                                           let username = conversationData["username"] as? String {
                                            
                                            let avatarUrl = conversationData["avatar_url"] as? String ?? ""
                                            let latestMessage = conversationData["latest_message"] as? String ?? ""
                                            
                                            // 处理时间字符串
                                            var messageTime = Date(timeIntervalSince1970: 0)  // 默认使用1970年
                                            if let timeString = conversationData["latest_message_time"] as? String {

                                                // 尝试多种时间格式
                                                var parsedDate: Date?

                                                // 1. 检查是否带有时区信息（Z结尾）
                                                if timeString.hasSuffix("Z") {
                                                    // 带时区的 ISO8601 格式
                                                    let iso8601Formatter = ISO8601DateFormatter()
                                                    iso8601Formatter.formatOptions = [.withInternetDateTime]
                                                    parsedDate = iso8601Formatter.date(from: timeString)
                                                } else {
                                                    // 2. 不带时区的格式，假设是北京时间
                                                    let customFormatter = DateFormatter()
                                                    customFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
                                                    customFormatter.timeZone = TimeZone(identifier: "Asia/Shanghai") // 北京时间
                                                    parsedDate = customFormatter.date(from: timeString)

                                                    if parsedDate == nil {
                                                        // 3. 尝试数据库格式：yyyy-MM-dd HH:mm:ss
                                                        customFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
                                                        customFormatter.timeZone = TimeZone(identifier: "Asia/Shanghai") // 北京时间
                                                        parsedDate = customFormatter.date(from: timeString)
                                                    }

                                                    if parsedDate == nil {
                                                        // 4. 尝试当前时区格式
                                                        customFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
                                                        customFormatter.timeZone = TimeZone.current
                                                        parsedDate = customFormatter.date(from: timeString)
                                                    }
                                                }

                                                if let date = parsedDate {
                                                    messageTime = date
                                                } else {
                                                    messageTime = Date(timeIntervalSince1970: 0)
                                                }
                                            }
                                            
                                            let unreadCount = conversationData["unread_count"] as? Int ?? 0
                                            let isMuted = conversationData["is_muted"] as? Bool ?? false

                                            let conversation = Conversation(
                                                conversation_id: conversationId,
                                                user_id: userId,
                                                username: username,
                                                avatar_url: avatarUrl,
                                                latest_message: latestMessage,
                                                latest_message_time: messageTime,
                                                unread_count: unreadCount,
                                                is_muted: isMuted
                                            )
                                            
                                            parsedConversations.append(conversation)
                                        }
                                    }
                                    
                                    // 检查删除列表中的会话是否有未读消息，如果有则恢复
                                    self?.checkAndRestoreConversationsWithUnreadMessages(parsedConversations)

                                    let filteredConversations = self?.filterConversations(parsedConversations) ?? []

                                    // 确保在主线程更新UI
                                    DispatchQueue.main.async {
                                        self?.conversations = filteredConversations
                                        self?.saveConversationsToCache()

                                        // 更新本地免打扰状态
                                        ConversationMuteManager.shared.updateMuteStates(from: filteredConversations)

                                        // 更新全局未读消息计数
                                        self?.updateGlobalUnreadCount()

                                        self?.forceRefreshUI()
                                    }
                                    return
                                } else if let emptyArray = responseData["conversations"] as? [Any], emptyArray.isEmpty {
                                    // 空数组，没有会话
                                    self?.conversations = []
                                    self?.saveConversationsToCache()
                                    return
                                }
                            }
                        } else {
                            // 请求不成功
                            if let message = json["message"] as? String {
                                self?.errorMessage = message
                            } else {
                                self?.errorMessage = "获取会话列表失败"
                            }
                            return
                        }
                        }
                    } catch {
                        self?.conversations = []
                        self?.saveConversationsToCache()
                    }
                }
            }
        }.resume()
    }

    /// 强制加载会话列表，忽略防抖机制
    func forceLoadConversations() {
        // 重置防抖时间
        lastLoadTime = Date.distantPast
        loadConversations()
    }

    func loadConversationsFromChatReturn(conversationId: String) {
        // 从聊天页返回的专用方法，有独立的防抖机制
        let now = Date()
        if now.timeIntervalSince(lastChatReturnTime) < chatReturnDebounceInterval {
            return
        }
        lastChatReturnTime = now


        // 重置普通防抖时间，确保可以立即执行
        lastLoadTime = Date.distantPast
        loadConversations()
    }

    func loadConversationsFromCache() {
        do {
            let fileManager = FileManager.default
            let documentsDirectory = try fileManager.url(for: .documentDirectory, in: .userDomainMask, appropriateFor: nil, create: false)
            let fileURL = documentsDirectory.appendingPathComponent("conversations_\(userId).json")
            
            // 检查文件是否存在
            if fileManager.fileExists(atPath: fileURL.path) {
                let data = try Data(contentsOf: fileURL)
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                conversations = try decoder.decode([Conversation].self, from: data)
            }
        } catch {
            print("读取缓存的会话列表失败: \(error)")
            // 不显示错误，因为这只是缓存读取失败
        }
    }
    
    func saveConversationsToCache() {
        do {
            let fileManager = FileManager.default
            let documentsDirectory = try fileManager.url(for: .documentDirectory, in: .userDomainMask, appropriateFor: nil, create: true)
            let fileURL = documentsDirectory.appendingPathComponent("conversations_\(userId).json")
            
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(conversations)
            try data.write(to: fileURL)
        } catch {
            print("保存会话列表到缓存失败: \(error)")
        }
    }
    
    func deleteConversation(_ conversation: Conversation) {
        // 只做本地操作，不调用后端接口

        // 删除本地数据并添加到过滤列表
        conversations.removeAll { $0.conversation_id == conversation.conversation_id }

        // 使用全局管理器添加到删除列表
        ConversationFilterManager.shared.addToDeletedList(conversationId: conversation.conversation_id, userId: userId)

        // 同步到本地变量（为了兼容现有逻辑）
        deletedConversationIds.insert(conversation.conversation_id)

        saveConversationsToCache()

        // 清除该会话的本地消息缓存
        clearConversationCache(conversationId: conversation.conversation_id)

    }

    // 清除指定会话的本地消息缓存
    private func clearConversationCache(conversationId: String) {
        do {
            let fileManager = FileManager.default
            let documentsDirectory = try fileManager.url(for: .documentDirectory, in: .userDomainMask, appropriateFor: nil, create: false)

            // 清除消息缓存文件
            let messageFileURL = documentsDirectory.appendingPathComponent("messages_\(conversationId).json")
            if fileManager.fileExists(atPath: messageFileURL.path) {
                try fileManager.removeItem(at: messageFileURL)
            }

            // 删除会话时不清除任何ID记录
            // 保留最后消息ID和已读消息ID，确保：
            // 1. 未读消息计算的连续性
            // 2. 从用户头像进入聊天时不会获取全部历史记录

            // 如果当前有ChatView实例正在显示这个会话，通知它清除数据
            NotificationCenter.default.post(
                name: NSNotification.Name("ConversationCacheCleared"),
                object: nil,
                userInfo: ["conversation_id": conversationId]
            )

        } catch {
            print("清除会话缓存失败: \(error)")
        }
    }

    /// 获取指定会话的本地最后一条消息
    /// - Parameter conversationId: 会话ID
    /// - Returns: 最后一条消息的内容，如果没有消息则返回"暂无消息"
    private func getLocalLastMessage(for conversationId: String) -> String {
        do {
            let fileManager = FileManager.default
            let documentsDirectory = try fileManager.url(for: .documentDirectory, in: .userDomainMask, appropriateFor: nil, create: false)
            let fileURL = documentsDirectory.appendingPathComponent("messages_\(conversationId).json")

            // 检查文件是否存在
            if fileManager.fileExists(atPath: fileURL.path) {
                let data = try Data(contentsOf: fileURL)
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                let messages = try decoder.decode([Message].self, from: data)

                // 按时间排序，获取最后一条消息
                let sortedMessages = messages.sorted { $0.created_at < $1.created_at }
                if let lastMessage = sortedMessages.last {
                    return lastMessage.content
                }
            }
        } catch {
            print("读取本地消息失败: \(error)")
        }

        return "暂无消息"
    }

    func handleNewMessage(_ messageData: [String: Any], conversationId: String) {
        // 智能更新会话列表，而不是重新加载整个列表
        guard let content = messageData["content"] as? String,
              let createdAtString = messageData["created_at"] as? String,
              let senderId = messageData["sender_id"] as? Int else {
            // 如果数据不完整，回退到重新加载
            loadConversations()
            return
        }

        // 解析时间
        let formatter = ISO8601DateFormatter()
        let messageTime = formatter.date(from: createdAtString) ?? Date()

        // 检查会话是否在删除列表中
        let wasDeleted = deletedConversationIds.contains(conversationId)
        if wasDeleted {
            // 这个会话之前被删除了，现在有新消息
            // 全局管理器会处理删除列表的移除，这里等待通知后重新加载
            // 不立即重新加载，等待ConversationFilterUpdated通知
            return
        }

        // 查找现有会话
        if let index = conversations.firstIndex(where: { $0.conversation_id == conversationId }) {
            // 更新现有会话
            var updatedConversation = conversations[index]
            updatedConversation.latest_message = content
            updatedConversation.latest_message_time = messageTime

            // 如果消息不是当前用户发送的，增加未读数
            if senderId != userId {
                updatedConversation.unread_count += 1
            }

            // 移除旧位置的会话
            conversations.remove(at: index)
            // 插入到列表顶部
            conversations.insert(updatedConversation, at: 0)

            saveConversationsToCache()
            // 更新全局未读消息计数
            updateGlobalUnreadCount()
            forceRefreshUI()
        } else {
            // 新会话，重新加载列表
            loadConversations()
        }
    }

    func markConversationAsRead(_ conversationId: String) {
        // 将指定会话标记为已读（清零未读数）
        if let index = conversations.firstIndex(where: { $0.conversation_id == conversationId }) {
            conversations[index].unread_count = 0
            saveConversationsToCache()
        }
    }

    func refreshConversationFromChat(_ conversationId: String) {
        // 从聊天页返回时刷新会话状态
        markConversationAsRead(conversationId)
        // 更新全局未读消息计数
        updateGlobalUnreadCount()
    }

    // 更新全局未读消息计数
    private func updateGlobalUnreadCount() {
        let totalUnreadCount = conversations.reduce(0) { total, conversation in
            // 只计算未被删除且未免打扰的会话的未读消息
            if !deletedConversationIds.contains(conversation.conversation_id) && !conversation.is_muted {
                return total + conversation.unread_count
            }
            return total
        }

        DispatchQueue.main.async {
            UserState.shared.setUnreadMessageCount(totalUnreadCount)
        }
    }

    // MARK: - 删除会话过滤相关方法

    /// 过滤会话列表，移除被删除的会话，并根据未读消息数量决定显示的消息内容
    private func filterConversations(_ conversations: [Conversation]) -> [Conversation] {
        let filtered = conversations.filter { conversation in
            let isDeleted = deletedConversationIds.contains(conversation.conversation_id)
            return !isDeleted
        }

        // 根据未读消息数量决定显示的消息内容
        let processedConversations = filtered.map { conversation in
            var updatedConversation = conversation

            // 如果未读消息数量为0，使用本地最后一条消息
            // 如果未读消息数量不为0，使用后端返回的消息（因为后端返回的是最新的未读消息）
            if conversation.unread_count == 0 {
                let localLastMessage = getLocalLastMessage(for: conversation.conversation_id)
                updatedConversation.latest_message = localLastMessage
            }
            // 如果 unread_count > 0，保持使用后端返回的 latest_message

            return updatedConversation
        }

        return processedConversations
    }

    /// 加载被删除的会话ID列表
    private func loadDeletedConversationIds() {
        do {
            let fileManager = FileManager.default
            let documentsDirectory = try fileManager.url(for: .documentDirectory, in: .userDomainMask, appropriateFor: nil, create: false)
            let fileURL = documentsDirectory.appendingPathComponent("deleted_conversations_\(userId).json")

            if fileManager.fileExists(atPath: fileURL.path) {
                let data = try Data(contentsOf: fileURL)
                let deletedIds = try JSONDecoder().decode([String].self, from: data)
                deletedConversationIds = Set(deletedIds)
            } else {
                deletedConversationIds = []
            }
        } catch {
            deletedConversationIds = []
        }
    }

    /// 保存被删除的会话ID列表
    private func saveDeletedConversationIds() {
        do {
            let fileManager = FileManager.default
            let documentsDirectory = try fileManager.url(for: .documentDirectory, in: .userDomainMask, appropriateFor: nil, create: false)
            let fileURL = documentsDirectory.appendingPathComponent("deleted_conversations_\(userId).json")

            let deletedIds = Array(deletedConversationIds)
            let data = try JSONEncoder().encode(deletedIds)
            try data.write(to: fileURL)
        } catch {
            print("保存删除的会话列表失败: \(error)")
        }
    }
}

struct ConversationListView: View {
    @StateObject private var viewModel: ConversationListViewModel
    @Environment(\.dismiss) var dismiss

    init(userId: Int) {
        _viewModel = StateObject(wrappedValue: ConversationListViewModel(userId: userId))
    }

    var filteredConversations: [Conversation] {
        // 过滤掉被删除的会话
        return viewModel.conversations.filter { conversation in
            let isDeleted = viewModel.deletedConversationIds.contains(conversation.conversation_id)
            return !isDeleted
        }
    }
    
    var body: some View {
        ZStack {
            Color.white.edgesIgnoringSafeArea(.all)

            VStack(spacing: 0) {
                // 标题
                HStack {
                    Spacer()
                    Text("聊天记录")
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.black)
                    Spacer()
                }
                .padding(.horizontal, 16)
                .padding(.top, 16)
                .padding(.bottom, 8)

                // 会话列表
                if viewModel.isLoading && viewModel.conversations.isEmpty {
                    Spacer()
                    ProgressView()
                    Spacer()
                } else if let errorMessage = viewModel.errorMessage, viewModel.conversations.isEmpty {
                    Spacer()
                    VStack {
                        Text(errorMessage)
                            .foregroundColor(.gray)

                        Button("重试") {
                            viewModel.loadConversations()
                        }
                        .padding()
                        .foregroundColor(.white)
                        .background(Color.blue)
                        .cornerRadius(8)
                        .padding(.top)
                    }
                    Spacer()
                } else if filteredConversations.isEmpty {
                    Spacer()
                    Text("暂无私信")
                        .foregroundColor(.gray)
                    Spacer()
                } else {
                    List {
                        ForEach(filteredConversations) { conversation in
                            NavigationLink(destination: ChatView(
                                conversationId: conversation.conversation_id,
                                otherUserId: conversation.user_id,
                                otherUsername: conversation.username,
                                userId: viewModel.userId,
                                otherUserAvatar: conversation.avatar_url
                            )) {
                                ConversationRow(conversation: conversation)
                            }
                            .listRowInsets(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16))
                            .listRowSeparator(.hidden)
                            .listRowBackground(Color.white)
                        }
                        .onDelete { indexSet in
                            for index in indexSet {
                                let conversation = filteredConversations[index]
                                viewModel.deleteConversation(conversation)
                            }
                        }
                    }
                    .listStyle(PlainListStyle())
                    .id(viewModel.refreshTrigger) // 使用refreshTrigger强制刷新
                    .refreshable {
                        viewModel.forceLoadConversations()
                    }
                }
            }
        }
        .onAppear {
            // 设置当前页面状态并通过WebSocket更新
            //UserState.shared.updatePageStatus(page: "ConversationListView", conversationId: "")

            // 不再在进入页面时立即清空未读消息数量
            // 只有当用户真正查看了所有会话后才清空
        }
    }
}

struct ConversationListView_Previews: PreviewProvider {
    static var previews: some View {
        ConversationListView(userId: 1)
    }
} 
