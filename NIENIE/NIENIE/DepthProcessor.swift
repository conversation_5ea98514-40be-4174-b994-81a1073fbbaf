import Foundation
import CoreML
import CoreImage
import UIKit
import Vision
import SwiftUI

// MARK: - 错误类型定义
enum DepthProcessingError: Error, LocalizedError {
    case modelLoadFailed
    case imageConversionFailed
    case inferenceError(String)
    case invalidInput
    case pixelBufferCreationFailed
    
    var errorDescription: String? {
        switch self {
        case .modelLoadFailed:
            return "深度模型加载失败"
        case .imageConversionFailed:
            return "图像转换失败"
        case .inferenceError(let message):
            return "深度推理失败: \(message)"
        case .invalidInput:
            return "输入图像无效"
        case .pixelBufferCreationFailed:
            return "像素缓冲区创建失败"
        }
    }
}

// MARK: - 光源数据模型
struct LightSource: Identifiable {
    let id = UUID()
    var position: CGPoint
    var direction: CGPoint // 聚光灯指向方向（相对于position的偏移）
    var color: Color
    var intensity: Float
    var radius: Float // 照射距离
    var coneAngle: Float // 光锥角度（度数）
    var depth: Float // 自动计算，基于人物深度

    init(position: CGPoint, direction: CGPoint = CGPoint(x: 0, y: 50), color: Color = .white, intensity: Float = 1.5, radius: Float = 3.0, coneAngle: Float = 45.0, depth: Float = 0.5) {
        self.position = position
        self.direction = direction
        self.color = color
        self.intensity = intensity
        self.radius = radius
        self.coneAngle = coneAngle
        self.depth = depth
    }

    /// 计算人物区域的平均深度值
    static func calculateHumanDepth(from depthMap: UIImage, humanMask: CVPixelBuffer) -> Float {
        guard let depthCGImage = depthMap.cgImage else { return 0.5 }

        let depthCIImage = CIImage(cgImage: depthCGImage)
        let maskCIImage = CIImage(cvPixelBuffer: humanMask)

        // 将遮罩缩放到与深度图相同大小
        let depthSize = depthCIImage.extent.size
        let maskSize = maskCIImage.extent.size
        let scaleX = depthSize.width / maskSize.width
        let scaleY = depthSize.height / maskSize.height
        let scaleTransform = CGAffineTransform(scaleX: scaleX, y: scaleY)
        let scaledMask = maskCIImage.transformed(by: scaleTransform)

        let context = CIContext()

        // 创建像素缓冲区来读取深度数据
        guard let depthPixelBuffer = createPixelBuffer(from: depthCIImage, context: context),
              let maskPixelBuffer = createPixelBuffer(from: scaledMask, context: context) else {
            return 0.5
        }

        // 计算人物区域的平均深度
        return calculateAverageDepthInMask(depthBuffer: depthPixelBuffer, maskBuffer: maskPixelBuffer)
    }

    private static func createPixelBuffer(from ciImage: CIImage, context: CIContext) -> CVPixelBuffer? {
        let width = Int(ciImage.extent.width)
        let height = Int(ciImage.extent.height)

        var pixelBuffer: CVPixelBuffer?
        let status = CVPixelBufferCreate(
            kCFAllocatorDefault,
            width,
            height,
            kCVPixelFormatType_32BGRA,
            nil,
            &pixelBuffer
        )

        guard status == kCVReturnSuccess, let buffer = pixelBuffer else {
            return nil
        }

        context.render(ciImage, to: buffer)
        return buffer
    }

    private static func calculateAverageDepthInMask(depthBuffer: CVPixelBuffer, maskBuffer: CVPixelBuffer) -> Float {
        CVPixelBufferLockBaseAddress(depthBuffer, .readOnly)
        CVPixelBufferLockBaseAddress(maskBuffer, .readOnly)
        defer {
            CVPixelBufferUnlockBaseAddress(depthBuffer, .readOnly)
            CVPixelBufferUnlockBaseAddress(maskBuffer, .readOnly)
        }

        guard let depthData = CVPixelBufferGetBaseAddress(depthBuffer),
              let _ = CVPixelBufferGetBaseAddress(maskBuffer) else {
            return 0.5
        }

        let width = CVPixelBufferGetWidth(depthBuffer)
        let height = CVPixelBufferGetHeight(depthBuffer)
        let depthBytesPerRow = CVPixelBufferGetBytesPerRow(depthBuffer)
        let maskBytesPerRow = CVPixelBufferGetBytesPerRow(maskBuffer)

        var totalDepth: Float = 0.0
        var pixelCount: Int = 0

        for y in 0..<height {
            for x in 0..<width {
                let depthPixelOffset = y * depthBytesPerRow + x * 4
                let maskPixelOffset = y * maskBytesPerRow + x * 4

                // 读取遮罩值（假设是灰度值）
                let maskValue = depthData.load(fromByteOffset: maskPixelOffset, as: UInt8.self)

                // 如果遮罩值大于阈值，说明是人物区域
                if maskValue > 128 {
                    // 读取深度值（假设是灰度值，0=近，255=远）
                    let depthValue = depthData.load(fromByteOffset: depthPixelOffset, as: UInt8.self)
                    totalDepth += Float(depthValue) / 255.0
                    pixelCount += 1
                }
            }
        }

        return pixelCount > 0 ? totalDepth / Float(pixelCount) : 0.5
    }
}

// MARK: - 处理状态
enum DepthLightingProcessingState {
    case idle
    case loadingDepth
    case processingLighting
    case completed
}

// MARK: - 深度处理器
class DepthProcessor: ObservableObject {
    private let context = CIContext()
    private var model: DepthAnythingV2SmallF32?
    private let targetSize = CGSize(width: 518, height: 392)

    @Published var isLoading = false
    @Published var error: String?

    init() {
        loadModel()
    }

    private func loadModel() {
        DispatchQueue.global(qos: .userInitiated).async {
            do {
                let model = try DepthAnythingV2SmallF32()
                DispatchQueue.main.async {
                    self.model = model
                }
            } catch {
                DispatchQueue.main.async {
                    self.error = "模型加载失败: \(error.localizedDescription)"
                }
            }
        }
    }

    /// 生成深度图
    func generateDepthMap(from image: UIImage, completion: @escaping (UIImage?) -> Void) {
        guard let model = model else {
            DispatchQueue.main.async {
                completion(nil)
            }
            return
        }

        guard let cgImage = image.cgImage else {
            DispatchQueue.main.async {
                completion(nil)
            }
            return
        }

        DispatchQueue.main.async {
            self.isLoading = true
        }

        DispatchQueue.global(qos: .userInitiated).async {
            do {
                let depthImage = try self.performDepthInference(cgImage: cgImage, model: model)
                DispatchQueue.main.async {
                    self.isLoading = false
                    completion(depthImage)
                }
            } catch {
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.error = "深度推理失败: \(error.localizedDescription)"
                    completion(nil)
                }
            }
        }
    }

    private func performDepthInference(cgImage: CGImage, model: DepthAnythingV2SmallF32) throws -> UIImage {
        let originalSize = CGSize(width: cgImage.width, height: cgImage.height)

        // 1. 创建输入像素缓冲区
        var inputPixelBuffer: CVPixelBuffer!
        let status = CVPixelBufferCreate(
            kCFAllocatorDefault,
            Int(targetSize.width),
            Int(targetSize.height),
            kCVPixelFormatType_32ARGB,
            nil,
            &inputPixelBuffer
        )

        guard status == kCVReturnSuccess else {
            throw DepthProcessingError.pixelBufferCreationFailed
        }

        // 2. 调整输入图像大小并渲染到像素缓冲区
        let inputImage = CIImage(cgImage: cgImage).resized(to: targetSize)
        context.render(inputImage, to: inputPixelBuffer)

        // 3. 执行模型推理
        let result = try model.prediction(image: inputPixelBuffer)

        // 4. 处理输出深度图
        let depthCIImage = CIImage(cvPixelBuffer: result.depth)
            .resized(to: originalSize)

        // 5. 转换为UIImage - 确保格式兼容Metal
        guard let outputCGImage = context.createCGImage(depthCIImage, from: depthCIImage.extent) else {
            throw DepthProcessingError.imageConversionFailed
        }

        // 创建兼容Metal的深度图格式
        let colorSpace = CGColorSpaceCreateDeviceGray()
        let bitmapInfo = CGImageAlphaInfo.none.rawValue

        guard let context = CGContext(
            data: nil,
            width: Int(originalSize.width),
            height: Int(originalSize.height),
            bitsPerComponent: 8,
            bytesPerRow: Int(originalSize.width),
            space: colorSpace,
            bitmapInfo: bitmapInfo
        ) else {
            return UIImage(cgImage: outputCGImage)
        }

        context.draw(outputCGImage, in: CGRect(origin: .zero, size: originalSize))

        guard let metalCompatibleCGImage = context.makeImage() else {
            return UIImage(cgImage: outputCGImage)
        }

        return UIImage(cgImage: metalCompatibleCGImage)
    }
}

// MARK: - CIImage 扩展
extension CIImage {
    func resized(to size: CGSize) -> CIImage {
        let outputScaleX = size.width / extent.width
        let outputScaleY = size.height / extent.height
        var outputImage = self.transformed(by: CGAffineTransform(scaleX: outputScaleX, y: outputScaleY))
        outputImage = outputImage.transformed(
            by: CGAffineTransform(translationX: -outputImage.extent.origin.x, y: -outputImage.extent.origin.y)
        )
        return outputImage
    }
}

// MARK: - Color扩展
extension Color {
    var cgColor: CGColor? {
        return UIColor(self).cgColor
    }
}
