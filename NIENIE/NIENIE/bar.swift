import SwiftUI

// 底部导航栏组件
struct BottomNavigationBar: View {
    @Binding var selectedTabIndex: Int
    let geometry: GeometryProxy
    @EnvironmentObject var userState: UserState
    @State private var showToast = false // 添加Toast提示状态
    @State private var toastMessage = "" // Toast提示消息
    
    // 导航栏项的数据结构
    struct NavigationItem {
        let title: String
        let icon: (selected: String, unselected: String) // 使用元组存储选中和未选中的图标名
    }
    
    // 导航栏项的数据
    let navigationItems = [
        NavigationItem(title: "社区", icon: (selected: "5-1", unselected: "5-8")),
        NavigationItem(title: "聊天", icon: (selected: "5-2", unselected: "5-2")), // 聊天的图标不变化
        NavigationItem(title: "我", icon: (selected: "5-9", unselected: "5-3"))
    ]
    
    var body: some View {
        ZStack {
            // 导航栏背景
            Rectangle()
                .fill(Color.white)
                .padding(.top, 15)
                .frame(height: geometry.size.height * 0.074+geometry.safeAreaInsets.bottom)
                .edgesIgnoringSafeArea(.bottom)
            
            
            // 导航图标
            HStack {
                // 社区按钮
                VStack(spacing: 4) {
                    Image(0 == selectedTabIndex ? navigationItems[0].icon.selected : navigationItems[0].icon.unselected)
                        .resizable()
                        .scaledToFit()
                        .frame(
                            width: geometry.size.width * 0.075,
                            height: geometry.size.height * 0.035
                        )
                    
                    Text(navigationItems[0].title)
                        .font(.system(
                            size: geometry.size.width * 0.032,
                            weight: .semibold
                        ))
                        .foregroundColor(0 == selectedTabIndex ? .black : Color(red: 0.27, green: 0.18, blue: 0.13))
                }
                .frame(maxWidth: .infinity)
                .onTapGesture {
                    selectedTabIndex = 0
                    userState.selectedTab = 0
                }
                
                // 聊天按钮
                VStack(spacing: 4) {
                    ZStack {
                        Image(systemName: "message")
                            .font(.system(size: geometry.size.width * 0.075))
                            .foregroundColor(1 == selectedTabIndex ? .black : Color(red: 0.27, green: 0.18, blue: 0.13))

                        // 未读消息感叹号图标
                        if userState.unreadMessageCount > 0 {
                            Image(systemName: "exclamationmark")
                                .font(.system(size: 8, weight: .bold))
                                .foregroundColor(.white)
                                .frame(width: 14, height: 14)
                                .background(Color.red)
                                .clipShape(Circle())
                                .offset(x: 12, y: -12)
                        }
                    }
                    .frame(width: geometry.size.width * 0.075, height: geometry.size.height * 0.035)

                    Text(navigationItems[1].title)
                        .font(.system(
                            size: geometry.size.width * 0.032,
                            weight: .semibold
                        ))
                        .foregroundColor(1 == selectedTabIndex ? .black : Color(red: 0.27, green: 0.18, blue: 0.13))
                }
                .frame(maxWidth: .infinity)
                .onTapGesture {
                    // 切换到聊天列表页面（标签页1）
                    selectedTabIndex = 1
                    userState.selectedTab = 1
                    // 点击聊天图标时清空未读消息数量
                    userState.clearUnreadMessageCount()
                }

                Spacer()
                
                // 我的按钮
                VStack(spacing: 4) {
                    Image(2 == selectedTabIndex ? navigationItems[2].icon.selected : navigationItems[2].icon.unselected)
                        .resizable()
                        .scaledToFit()
                        .frame(
                            width: geometry.size.width * 0.075,
                            height: geometry.size.height * 0.035
                        )
                    
                    Text(navigationItems[2].title)
                        .font(.system(
                            size: geometry.size.width * 0.032,
                            weight: .semibold
                        ))
                        .foregroundColor(2 == selectedTabIndex ? .black : Color(red: 0.27, green: 0.18, blue: 0.13))
                }
                .frame(maxWidth: .infinity)
                .onTapGesture {
                    selectedTabIndex = 2
                    userState.selectedTab = 2
                }
            }
            .padding(.horizontal, geometry.size.width * 0.05)
            .padding(.top, 30)
            .padding(.bottom, geometry.safeAreaInsets.bottom)
        }
        .frame(height: geometry.size.height * 0.074 + geometry.safeAreaInsets.bottom)
        .overlay(
            // 使用overlay添加Toast，而不是直接应用修饰符
            Group {
                if showToast {
                    VStack {
                        Spacer()
                        Text(toastMessage)
                            .font(Font.custom("PingFang SC", size: 14))
                            .foregroundColor(.white)
                            .padding(.horizontal, 20)
                            .padding(.vertical, 10)
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(Color.black.opacity(0.7))
                            )
                            .padding(.bottom, 100) // 确保Toast显示在导航栏上方
                            .transition(.opacity)
                            .onAppear {
                                // 2秒后自动隐藏Toast
                                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                                    withAnimation {
                                        showToast = false
                                    }
                                }
                            }
                    }
                }
            }
        )
        .onAppear {
            // 确保初始状态同步
            selectedTabIndex = userState.selectedTab
        }
        .onChange(of: userState.selectedTab) { oldValue, newValue in
            // 当userState.selectedTab变化时，同步更新selectedTabIndex
            selectedTabIndex = newValue
        }
    }
}
