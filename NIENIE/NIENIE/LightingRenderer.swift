import Foundation
import CoreImage
import UIKit
import Metal
import MetalKit

// MARK: - 光照渲染器
class LightingRenderer: ObservableObject {
    private let context = CIContext()
    private var device: MTLDevice?
    private var commandQueue: MTLCommandQueue?
    private var pipelineState: MTLComputePipelineState?
    
    init() {
        setupMetal()
    }
    
    private func setupMetal() {
        device = MTLCreateSystemDefaultDevice()
        commandQueue = device?.makeCommandQueue()
        
        guard let device = device else { return }
        
        // 创建Metal着色器
        let shaderSource = """
        #include <metal_stdlib>
        using namespace metal;

        kernel void spotlightKernel(texture2d<float, access::read> originalTexture [[texture(0)]],
                                   texture2d<float, access::read> depthTexture [[texture(1)]],
                                   texture2d<float, access::write> outputTexture [[texture(2)]],
                                   constant float4& lightPosition [[buffer(0)]],
                                   constant float4& lightDirection [[buffer(1)]],
                                   constant float4& lightColor [[buffer(2)]],
                                   constant float& lightIntensity [[buffer(3)]],
                                   constant float& lightRadius [[buffer(4)]],
                                   constant float& coneAngle [[buffer(5)]],
                                   constant float& lightDepth [[buffer(6)]],
                                   uint2 gid [[thread_position_in_grid]]) {

            if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
                return;
            }

            float4 originalColor = originalTexture.read(gid);
            float depth = depthTexture.read(gid).r;

            // 像素坐标（不归一化）
            float2 texSize = float2(originalTexture.get_width(), originalTexture.get_height());
            float2 pixelPos = float2(gid);
            float2 lightPos = lightPosition.xy;
            float2 lightDir = lightDirection.xy; // 已经在CPU端归一化

            // 计算从光源到像素的向量
            float2 toPixel = pixelPos - lightPos;
            float distance = length(toPixel);

            // 计算聚光灯方向衰减
            float2 toPixelNormalized = normalize(toPixel);
            float dotProduct = dot(lightDir, toPixelNormalized);
            // 手动转换角度到弧度 (Metal没有radians函数)
            float halfConeAngle = (coneAngle * 3.14159265359 / 180.0) * 0.5;
            float cosHalfCone = cos(halfConeAngle);

            // 聚光灯锥形衰减 - 只在锥形范围内有光照
            float spotFactor = 0.0;
            if (dotProduct > cosHalfCone) {
                // 在光锥内，计算平滑衰减
                float edgeSoftness = 0.05; // 减少边缘柔化，让边界更清晰
                spotFactor = smoothstep(cosHalfCone - edgeSoftness, cosHalfCone + edgeSoftness, dotProduct);
                spotFactor = pow(spotFactor, 1.5); // 调整中心亮度分布
            }

            // 深度差异计算
            float depthDiff = abs(depth - lightDepth);
            float depthFactor = exp(-depthDiff * 2.0);

            // 锥形距离衰减 - 只在锥形范围内计算距离衰减
            float attenuation = 0.0;
            if (spotFactor > 0.0) {
                // 计算沿光照方向的距离
                float projectedDistance = dot(toPixel, lightDir);
                float maxRadius = lightRadius * 300.0;

                // 确保在有效照射距离内
                if (projectedDistance > 0 && projectedDistance < maxRadius) {
                    attenuation = 1.0 / (1.0 + 0.002 * distance + 0.00005 * distance * distance);
                    attenuation *= smoothstep(maxRadius, maxRadius * 0.1, projectedDistance);
                }
            }

            // 最终光照强度 - 适配0-1强度范围，增强聚光灯效果
            float finalIntensity = lightIntensity * attenuation * depthFactor * spotFactor * 12.0;

            // 光照混合 - 确保完全保持原始像素值
            float4 finalColor = originalColor;

            // 只有在真正有光照效果时才修改像素
            if (finalIntensity > 0.001) {  // 使用更严格的阈值
                float3 lightContribution = lightColor.rgb * finalIntensity;
                float3 enhancedColor = originalColor.rgb * (1.0 + finalIntensity * 0.5) + lightContribution * 2.0;
                finalColor.rgb = clamp(enhancedColor, 0.0, 1.0);
            }
            // 否则完全保持原始颜色不变

            outputTexture.write(finalColor, gid);
        }
        """
        
        do {
            let library = try device.makeLibrary(source: shaderSource, options: nil)
            let function = library.makeFunction(name: "spotlightKernel")!
            pipelineState = try device.makeComputePipelineState(function: function)
        } catch {
            print("Metal setup failed: \(error)")
        }
    }
    
    /// 基于深度图和光源渲染光照效果
    func renderLighting(
        originalImage: UIImage,
        depthMap: UIImage,
        lightSources: [LightSource],
        completion: @escaping (UIImage?) -> Void
    ) {
        

        DispatchQueue.global(qos: .userInitiated).async {
            guard let originalCGImage = originalImage.cgImage,
                  let depthCGImage = depthMap.cgImage else {
                DispatchQueue.main.async {
                    completion(nil)
                }
                return
            }

            let result = self.performLightingCalculation(
                originalImage: originalCGImage,
                depthMap: depthCGImage,
                lightSources: lightSources
            )

            DispatchQueue.main.async {
                
                completion(result)
            }
        }
    }
    
    private func performLightingCalculation(
        originalImage: CGImage,
        depthMap: CGImage,
        lightSources: [LightSource]
    ) -> UIImage? {
        
        // 如果Metal不可用，使用CPU渲染
        if device == nil || pipelineState == nil {
            return performCPULightingCalculation(
                originalImage: originalImage,
                depthMap: depthMap,
                lightSources: lightSources
            )
        }
        
        return performMetalLightingCalculation(
            originalImage: originalImage,
            depthMap: depthMap,
            lightSources: lightSources
        )
    }
    
    private func performMetalLightingCalculation(
        originalImage: CGImage,
        depthMap: CGImage,
        lightSources: [LightSource]
    ) -> UIImage? {
        
        guard let device = device,
              let commandQueue = commandQueue,
              let pipelineState = pipelineState else {
            return nil
        }
        
        // 创建纹理
        let textureLoader = MTKTextureLoader(device: device)

       
        do {
            let originalTexture = try textureLoader.newTexture(cgImage: originalImage, options: [
                MTKTextureLoader.Option.textureUsage: MTLTextureUsage.shaderRead.rawValue,
                MTKTextureLoader.Option.textureStorageMode: MTLStorageMode.shared.rawValue,
                MTKTextureLoader.Option.SRGB: false  // 禁用sRGB转换，保持原始色彩
            ])

            // 为深度图创建纹理时使用更兼容的选项
            let depthTexture = try textureLoader.newTexture(cgImage: depthMap, options: [
                MTKTextureLoader.Option.textureUsage: MTLTextureUsage.shaderRead.rawValue,
                MTKTextureLoader.Option.textureStorageMode: MTLStorageMode.shared.rawValue,
                MTKTextureLoader.Option.SRGB: false  // 深度图不使用sRGB色彩空间
            ])
            
            // 创建输出纹理
            let textureDescriptor = MTLTextureDescriptor.texture2DDescriptor(
                pixelFormat: .rgba8Unorm,
                width: originalTexture.width,
                height: originalTexture.height,
                mipmapped: false
            )
            textureDescriptor.usage = [.shaderWrite, .shaderRead]
            
            guard let outputTexture = device.makeTexture(descriptor: textureDescriptor) else {
                return nil
            }
            
            // 复制原始图像到输出纹理
            let commandBuffer = commandQueue.makeCommandBuffer()!
            let blitEncoder = commandBuffer.makeBlitCommandEncoder()!
            blitEncoder.copy(from: originalTexture, to: outputTexture)
            blitEncoder.endEncoding()
            
            // 为每个光源应用光照
            for lightSource in lightSources {
                let computeEncoder = commandBuffer.makeComputeCommandEncoder()!
                computeEncoder.setComputePipelineState(pipelineState)
                
                computeEncoder.setTexture(originalTexture, index: 0)
                computeEncoder.setTexture(depthTexture, index: 1)
                computeEncoder.setTexture(outputTexture, index: 2)
                
                // 设置聚光灯参数
                var lightPosition = simd_float4(Float(lightSource.position.x), Float(lightSource.position.y), 0, 0)

                // 归一化方向向量
                let dirLength = sqrt(lightSource.direction.x * lightSource.direction.x + lightSource.direction.y * lightSource.direction.y)
                let normalizedDirX = dirLength > 0 ? Float(lightSource.direction.x / dirLength) : 0.0
                let normalizedDirY = dirLength > 0 ? Float(lightSource.direction.y / dirLength) : 1.0
                var lightDirection = simd_float4(normalizedDirX, normalizedDirY, 0, 0)

                // 从SwiftUI Color转换为RGBA
                let uiColor = UIColor(lightSource.color)
                var red: CGFloat = 0
                var green: CGFloat = 0
                var blue: CGFloat = 0
                var alpha: CGFloat = 0
                uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)

                var lightColor = simd_float4(Float(red), Float(green), Float(blue), Float(alpha))
                var lightIntensity = lightSource.intensity * 2.0 // 增强光照强度
                var lightRadius = lightSource.radius
                var coneAngle = lightSource.coneAngle
                var lightDepth = lightSource.depth

                computeEncoder.setBytes(&lightPosition, length: MemoryLayout<simd_float4>.size, index: 0)
                computeEncoder.setBytes(&lightDirection, length: MemoryLayout<simd_float4>.size, index: 1)
                computeEncoder.setBytes(&lightColor, length: MemoryLayout<simd_float4>.size, index: 2)
                computeEncoder.setBytes(&lightIntensity, length: MemoryLayout<Float>.size, index: 3)
                computeEncoder.setBytes(&lightRadius, length: MemoryLayout<Float>.size, index: 4)
                computeEncoder.setBytes(&coneAngle, length: MemoryLayout<Float>.size, index: 5)
                computeEncoder.setBytes(&lightDepth, length: MemoryLayout<Float>.size, index: 6)
                
                let threadsPerGroup = MTLSize(width: 16, height: 16, depth: 1)
                let groupsPerGrid = MTLSize(
                    width: (outputTexture.width + threadsPerGroup.width - 1) / threadsPerGroup.width,
                    height: (outputTexture.height + threadsPerGroup.height - 1) / threadsPerGroup.height,
                    depth: 1
                )
                
                computeEncoder.dispatchThreadgroups(groupsPerGrid, threadsPerThreadgroup: threadsPerGroup)
                computeEncoder.endEncoding()
            }
            
            commandBuffer.commit()
            commandBuffer.waitUntilCompleted()
            
            // 将结果转换为UIImage
            return textureToUIImage(texture: outputTexture)
            
        } catch {
           
            // 如果Metal失败，尝试CPU渲染作为备选
            return performCPULightingCalculation(
                originalImage: originalImage,
                depthMap: depthMap,
                lightSources: lightSources
            )
        }
    }
    
    private func textureToUIImage(texture: MTLTexture) -> UIImage? {
        let width = texture.width
        let height = texture.height
        let bytesPerPixel = 4
        let bytesPerRow = width * bytesPerPixel
        let totalBytes = height * bytesPerRow
        
        let buffer = UnsafeMutableRawPointer.allocate(byteCount: totalBytes, alignment: 1)
        defer { buffer.deallocate() }
        
        texture.getBytes(buffer, bytesPerRow: bytesPerRow, from: MTLRegionMake2D(0, 0, width, height), mipmapLevel: 0)
        
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bitmapInfo = CGBitmapInfo.byteOrder32Big.rawValue | CGImageAlphaInfo.premultipliedLast.rawValue
        
        guard let context = CGContext(
            data: buffer,
            width: width,
            height: height,
            bitsPerComponent: 8,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: bitmapInfo
        ) else {
            return nil
        }
        
        guard let cgImage = context.makeImage() else {
            return nil
        }
        
        return UIImage(cgImage: cgImage)
    }
    
    private func performCPULightingCalculation(
        originalImage: CGImage,
        depthMap: CGImage,
        lightSources: [LightSource]
    ) -> UIImage? {

        let ciOriginal = CIImage(cgImage: originalImage)
        var resultImage = ciOriginal

        for lightSource in lightSources {
            // 创建径向渐变光照效果
            let radialGradient = CIFilter(name: "CIRadialGradient")!

            // 光源中心点
            let center = CIVector(x: lightSource.position.x, y: ciOriginal.extent.height - lightSource.position.y)
            radialGradient.setValue(center, forKey: "inputCenter")

            // 光照范围 - 大幅增强半径效果
            let radius = lightSource.radius * 200.0
            radialGradient.setValue(0, forKey: "inputRadius0")
            radialGradient.setValue(radius, forKey: "inputRadius1")

            // 光源颜色 - 增强强度效果
            let uiColor = UIColor(lightSource.color)
            var red: CGFloat = 0, green: CGFloat = 0, blue: CGFloat = 0, alpha: CGFloat = 0
            uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)

            // 增强光照强度
            let intensity = min(lightSource.intensity * 0.8, 1.0)
            let lightColor = CIColor(red: red * CGFloat(intensity), green: green * CGFloat(intensity), blue: blue * CGFloat(intensity), alpha: CGFloat(intensity))
            let transparentColor = CIColor(red: 0, green: 0, blue: 0, alpha: 0)

            radialGradient.setValue(lightColor, forKey: "inputColor0")
            radialGradient.setValue(transparentColor, forKey: "inputColor1")

            if let gradientImage = radialGradient.outputImage {
                // 裁剪到原图大小
                let croppedGradient = gradientImage.cropped(to: ciOriginal.extent)

                // 使用加法混合模式
                let blendFilter = CIFilter(name: "CIAdditionCompositing")!
                blendFilter.setValue(resultImage, forKey: kCIInputBackgroundImageKey)
                blendFilter.setValue(croppedGradient, forKey: kCIInputImageKey)

                if let blendedImage = blendFilter.outputImage {
                    resultImage = blendedImage
                }
            }
        }

        guard let outputCGImage = context.createCGImage(resultImage, from: resultImage.extent) else {
            return nil
        }

        return UIImage(cgImage: outputCGImage)
    }
}
