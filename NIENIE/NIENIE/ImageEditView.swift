import SwiftUI
import UIKit

// 为了保持向后兼容性，我们保留一个简单的重定向
struct ImageEditView: View {
    @Environment(\.presentationMode) var presentationMode
    let image: UIImage

    var body: some View {
        // 直接导航到图像调整页面，使用选择的图片作为背景图片
        ImageAdjustmentView(
            foregroundImage: nil, // 不设置默认人物图
            backgroundImage: image, // 将传入的图片作为背景图
            onBack: {
                // 返回到上一页
                presentationMode.wrappedValue.dismiss()
            }
        )
    }
}

// 图像调节视图
// 模糊强度乘数 - 调整此值可以改变模糊的整体强度
private let BLUR_INTENSITY_MULTIPLIER: Double = 20.0

struct ImageAdjustmentView: View {
    @State var foregroundImage: UIImage?
    @State var backgroundImage: UIImage?
    @State private var blurAmount: Double = 0
    @State private var showForeground: Bool = true
    @State private var needsReinitializeForeground = false // 标记是否需要重新初始化前景图
    @State private var rotationAngle: Double = 0
    @State private var showRotationWheel: Bool = false
    @State private var isFlipped: Bool = false
    @State private var foregroundOffset: CGSize = .zero
    @State private var foregroundScale: CGFloat = 1.0
    @State private var dragStartLocation: CGPoint = .zero
    @State private var lastScale: CGFloat = 1.0
    // 添加用于存储图片展示信息的状态变量
    @State private var backgroundDisplayInfo: DisplayInfo = .zero
    @State private var foregroundDisplayInfo: DisplayInfo = .zero
    // 添加边界框跟踪信息
    @State private var boundingBoxTopLeft: CGPoint = .zero
    @State private var boundingBoxSize: CGSize = .zero
    // 添加键盘状态管理
    @FocusState private var isTextFieldFocused: Bool
    // 添加新的状态变量
    @State private var showBackgroundSourcePicker = false // 更换背景弹窗
    @State private var showCharacterSourcePicker = false // 选择人物弹窗
    @State private var showTemplateGallery = false // 模版画廊
    @State private var showImagePicker = false
    @State private var showCamera = false
    @State private var selectedImage: UIImage?
    @State private var isSelectingBackground = false // 标记是否在选择背景

    // 人物裁剪相关状态变量
    @State private var showCharacterCropping = false // 显示人物裁剪界面
    @State private var characterImageToCrop: UIImage? // 待裁剪的人物图片
    @State private var cropFrameRect: CGRect = CGRect(x: 50, y: 100, width: 200, height: 300) // 裁剪框位置和大小
    @State private var isDraggingCropFrame = false // 是否正在拖拽裁剪框
    @State private var dragOffset: CGSize = .zero // 拖拽偏移量
    @State private var isResizing = false // 是否正在调整大小
    @State private var resizeStartSize: CGSize = .zero // 调整大小开始时的尺寸

    var onBack: () -> Void
    
    // 创建图片显示信息结构体
    struct DisplayInfo {
        var scale: CGFloat
        var size: CGSize
        var displaySize: CGSize
        var position: CGPoint
        
        static var zero: DisplayInfo {
            return DisplayInfo(scale: 1.0, size: .zero, displaySize: .zero, position: .zero)
        }
    }
    
    // 在ImageAdjustmentView结构体内添加新的状态变量
    @State private var blurDebounceTimer: Timer?
    @State private var blurredImageCache: [Double: UIImage] = [:]
    @State private var displayBlurAmount: Double = 0
    @State private var isProcessingBlur: Bool = false
    
    init(foregroundImage: UIImage? = nil, backgroundImage: UIImage? = nil, onBack: @escaping () -> Void = {}) {
        self._foregroundImage = State(initialValue: foregroundImage)
        self._backgroundImage = State(initialValue: backgroundImage)
        self.onBack = onBack
    }

    // 从路径加载图片的辅助方法
    private func loadImageFromPath(_ path: String) -> UIImage? {
        // 如果是网络路径，需要异步加载
        if path.hasPrefix("http") {
            // 这里暂时返回nil，后续可以实现异步加载
            return nil
        } else {
            // 本地路径
            return UIImage(contentsOfFile: path)
        }
    }

    // 加载模板背景图片
    private func loadTemplateBackground() {
        let templatePath = UserState.shared.templateBackgroundPath

        // 构建完整的图片URL
        let imageURL: URL?
        if templatePath.hasPrefix("http") {
            imageURL = URL(string: templatePath)
        } else {
            // 使用APIService构建URL
            imageURL = APIService.shared.getImageURL(filename: URL(string: templatePath)?.lastPathComponent ?? templatePath, isForeground: true)
        }

        guard let url = imageURL else {
            return
        }

        // 异步加载网络图片
        loadNetworkImage(from: url.absoluteString) { image in
            DispatchQueue.main.async {
                if let image = image {
                    self.backgroundImage = image
                } 
            }
        }
    }

    // 从网络加载图片的辅助方法
    private func loadNetworkImage(from urlString: String, completion: @escaping (UIImage?) -> Void) {
        guard let url = URL(string: urlString) else {
            completion(nil)
            return
        }

        URLSession.shared.dataTask(with: url) { data, response, error in
            guard let data = data, error == nil, let image = UIImage(data: data) else {
                completion(nil)
                return
            }
            completion(image)
        }.resume()
    }

    // 处理返回导航的方法
    private func handleBackNavigation() {
        // 如果是从模板创作进入的，需要特殊处理
        if UserState.shared.isCreatingFromTemplate {
            // 重置模板创作状态
            UserState.shared.resetTemplateCreationState()

            // 返回到图片详情页或社区页
            // 这里使用通用的dismiss方法，让系统自动返回到上一个页面
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let rootViewController = windowScene.windows.first?.rootViewController {

                var currentController = rootViewController
                while let presented = currentController.presentedViewController {
                    currentController = presented
                }

                currentController.dismiss(animated: true, completion: nil)
            }
        } else {
            // 普通返回逻辑
            onBack()
        }
    }
    
    var body: some View {
        ZStack {
            // 背景颜色
            Color.white.ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 标题栏
                HStack {
                    // 返回按钮
                    Button(action: {
                        handleBackNavigation()
                    }) {
                        Image(systemName: "arrow.left")
                            .font(.system(size: 18))
                            .foregroundColor(.black)
                    }
                    .padding(.leading, 16)

                    Spacer()

                    // 两个功能按钮
                    HStack(spacing: 20) {
                        // 更换背景按钮
                        Button(action: {
                            showBackgroundSourcePicker = true
                        }) {
                            Text("更换背景")
                                .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                                .foregroundColor(.black)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                )
                        }

                        // 选择人物按钮
                        Button(action: {
                            showCharacterSourcePicker = true
                        }) {
                            Text("选择人物")
                                .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                                .foregroundColor(.black)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                )
                        }
                    }

                    Spacer()

                    // 占位，保持按钮居中
                    Color.clear
                        .frame(width: 20, height: 20)
                        .padding(.trailing, 16)
                }
                .padding(.top, 16)
                .padding(.bottom, 10)
                
                // 图片展示区域 - 占据所有可用空间
                GeometryReader { geometry in
                    ZStack {
                        // 背景图片
                        if let backgroundImg = backgroundImage {
                            ZStack {
                                // 使用CIFilter对背景图进行模糊处理，与发送到后端的图像保持一致
                                if displayBlurAmount > 0 {
                                    // 使用缓存的模糊图像或处理过的模糊图像
                                    if let cachedImage = blurredImageCache[displayBlurAmount] {
                                        Image(uiImage: cachedImage)
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: geometry.size.width, height: geometry.size.height)
                                            .overlay(
                                                isProcessingBlur ?
                                                    ProgressView()
                                                        .scaleEffect(1.0)
                                                        .opacity(0.7) : nil
                                            )
                                    } else {
                                        // 显示原始图像，直到模糊处理完成
                                        Image(uiImage: backgroundImg)
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: geometry.size.width, height: geometry.size.height)
                                            .overlay(
                                                isProcessingBlur ?
                                                    ProgressView()
                                                        .scaleEffect(1.0)
                                                        .opacity(0.7) : nil
                                            )
                                    }
                                } else {
                                    // 原始图像
                                    Image(uiImage: backgroundImg)
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: geometry.size.width, height: geometry.size.height)
                                }

                                // 使用覆盖层代替background来获取几何信息
                                GeometryReader { imageGeo in
                                    Color.clear
                                        .onAppear {
                                            calculateBackgroundDisplayInfo(backgroundImg: backgroundImg, geometry: geometry)

                                            // 如果是初次加载且有前景图，初始化前景图显示信息
                                            if foregroundDisplayInfo.size == .zero && foregroundImage != nil {
                                                initializeForegroundDisplay(geometry: geometry)
                                            }
                                        }
                                        .onChange(of: backgroundImage) { _ in
                                            // 背景图片变化时重新计算显示信息
                                            if let backgroundImg = backgroundImage {
                                                calculateBackgroundDisplayInfo(backgroundImg: backgroundImg, geometry: geometry)

                                                // 如果标记了需要重新初始化前景图（更换背景后）
                                                if needsReinitializeForeground && foregroundImage != nil {
                                                    needsReinitializeForeground = false
                                                    initializeForegroundDisplay(geometry: geometry)

                                                    // 初始化完成后显示前景图
                                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                                        self.showForeground = true
                                                    }
                                                }
                                            }
                                        }
                                }
                            }
                        } else {
                            // 没有背景图片时，显示灰色背景并初始化前景图显示
                            Rectangle()
                                .fill(Color.gray.opacity(0.2))
                                .frame(width: geometry.size.width, height: geometry.size.height)
                                .onAppear {
                                    // 没有背景图时，使用默认的显示信息
                                    backgroundDisplayInfo = DisplayInfo(
                                        scale: 1.0,
                                        size: CGSize(width: geometry.size.width, height: geometry.size.height),
                                        displaySize: CGSize(width: geometry.size.width, height: geometry.size.height),
                                        position: CGPoint(x: 0, y: 0)
                                    )

                                    // 初始化前景图显示信息
                                    initializeForegroundDisplay(geometry: geometry)
                                }
                        }
                        
                        // 前景图片与边界框叠加在一起
                        if showForeground {
                            ZStack {
                                // 边界框 - 始终与人物图大小一致
                                Rectangle()
                                    .stroke(Color.blue, lineWidth: 2)
                                    .frame(
                                        width: foregroundDisplayInfo.displaySize.width * foregroundScale,
                                        height: foregroundDisplayInfo.displaySize.height * foregroundScale
                                    )
                                
                                // 前景图片（人物）
                                if let foregroundImg = foregroundImage {
                                    Image(uiImage: foregroundImg)
                                        .resizable()
                                        .scaledToFit()
                                        .opacity(0.5) // 设置透明度为50%
                                        .frame(
                                            width: foregroundDisplayInfo.displaySize.width * foregroundScale,
                                            height: foregroundDisplayInfo.displaySize.height * foregroundScale
                                        )
                                }
                            }
                            .scaleEffect(x: isFlipped ? -1 : 1, y: 1) // 水平翻转
                            .rotationEffect(.degrees(rotationAngle), anchor: .center) // 旋转，指定围绕中心旋转
                            .position(
                                x: backgroundDisplayInfo.position.x + foregroundOffset.width + (foregroundDisplayInfo.displaySize.width * foregroundScale / 2),
                                y: backgroundDisplayInfo.position.y + foregroundOffset.height + (foregroundDisplayInfo.displaySize.height * foregroundScale / 2)
                            )
                            .gesture(
                                DragGesture()
                                    .onChanged { value in
                                        // 计算新的偏移
                                        let newOffsetX = foregroundOffset.width + value.translation.width - dragStartLocation.x
                                        let newOffsetY = foregroundOffset.height + value.translation.height - dragStartLocation.y
                                        
                                        // 限制左上角不能超出背景图范围
                                        let minX: CGFloat = 0
                                        let minY: CGFloat = 0

                                        // 计算前景图当前显示尺寸
                                        let currentForegroundWidth = foregroundDisplayInfo.displaySize.width * foregroundScale
                                        let currentForegroundHeight = foregroundDisplayInfo.displaySize.height * foregroundScale

                                        // 确保前景图不会完全移出背景图范围
                                        // 最大偏移 = 背景图尺寸 - 前景图尺寸（但至少保留一部分前景图在背景图内）
                                        let maxX = backgroundDisplayInfo.displaySize.width - min(currentForegroundWidth, 50)
                                        let maxY = backgroundDisplayInfo.displaySize.height - min(currentForegroundHeight, 50)
                                        
                                        // 限制偏移量，保证左上角不超出背景图范围
                                        foregroundOffset = CGSize(
                                            width: max(minX, min(maxX, newOffsetX)),
                                            height: max(minY, min(maxY, newOffsetY))
                                        )
                                        
                                        dragStartLocation = CGPoint(x: value.translation.width, y: value.translation.height)
                                        
                                        // 计算并输出边界框信息
                                        updateBoundingBoxInfo()
                                    }
                                    .onEnded { _ in
                                        dragStartLocation = .zero
                                    }
                            )
                            .gesture(
                                MagnificationGesture(minimumScaleDelta: 0.01)
                                    .onChanged { value in
                                        // 防止缩放值过小导致不响应
                                        if abs(value - 1.0) < 0.01 {
                                            return
                                        }
                                        
                                        let delta = value / lastScale
                                        lastScale = value
                                        
                                        // 限制缩放范围
                                        let newScale = foregroundScale * delta
                                        foregroundScale = max(0.5, min(3.0, newScale))
                                        
                                        // 确保缩放后图像左上角仍不超出背景图左上角
                                        if foregroundOffset.width < 0 {
                                            foregroundOffset.width = 0
                                        }
                                        
                                        if foregroundOffset.height < 0 {
                                            foregroundOffset.height = 0
                                        }
                                        
                                        // 计算并输出边界框信息
                                        updateBoundingBoxInfo()
                                    }
                                    .onEnded { _ in
                                        lastScale = 1.0
                                    }
                            )
                            // 添加双指缩放功能，适用于模拟器测试
                            .onTapGesture(count: 2) {
                                // 双击时放大或缩小
                                if foregroundScale > 1.5 {
                                    // 如果已经放大，则缩小到初始大小
                                    foregroundScale = 1.0
                                } else {
                                    // 否则放大到2倍
                                    foregroundScale = 2.0
                                }
                                
                                // 确保左上角不超出背景图
                                if foregroundOffset.width < 0 {
                                    foregroundOffset.width = 0
                                }
                                
                                if foregroundOffset.height < 0 {
                                    foregroundOffset.height = 0
                                }
                                
                                // 计算并输出边界框信息
                                updateBoundingBoxInfo()
                            }
                        }
                    }
                }
                .clipped() // 防止内容溢出
                
                // 底部控制区域
                VStack(spacing: 20) {
                    // 背景模糊控制区域
                    VStack(alignment: .leading, spacing: 10) {
                        HStack {
                            Text("背景模糊")
                                .font(Font.custom("PingFang SC", size: 16))
                                .foregroundColor(.black)
                                .frame(width: 80, alignment: .leading)
                            
                            // 模糊度滑块 - 使用自定义滑块
                            ZStack(alignment: .center) {
                                // 滑块轨道
                                Rectangle()
                                    .fill(Color.gray.opacity(0.2))
                                    .frame(height: 4)
                                    .cornerRadius(2)
                                
                                // 滑块
                                GeometryReader { geo in
                                    let width = geo.size.width
                                    let knobPosition = width * blurAmount
                                    
                                    // 滑块
                                    Image("m-2")
                                        .resizable()
                                        .frame(width: 20, height: 20)
                                        .position(x: knobPosition, y: geo.size.height / 2)
                                        .gesture(
                                            DragGesture()
                                                .onChanged { value in
                                                    let percentage = max(0, min(width, value.location.x)) / width
                                                    blurAmount = percentage
                                                        
                                                    // 取消之前的定时器
                                                    blurDebounceTimer?.invalidate()
                                                        
                                                    // 创建新的定时器，延迟300毫秒后应用模糊效果
                                                    blurDebounceTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: false) { _ in
                                                        applyBlurEffect(blurAmount: blurAmount)
                                                    }
                                                }
                                        )
                                }
                                .frame(height: 30)
                            }
                            
                            // 显示百分比
                            Text("\(Int(blurAmount * 100))%")
                                .font(Font.custom("PingFang SC", size: 14))
                                .foregroundColor(.gray)
                                .frame(width: 40)
                        }
                    }
                    .padding(.horizontal, 20)
                    
                    // 人物调节区域
                    VStack(alignment: .leading, spacing: 10) {
                        HStack {
                            Text("人物调节")
                                .font(Font.custom("PingFang SC", size: 16))
                                .foregroundColor(.black)
                            
                            Spacer()
                            
                            // 隐藏/显示人物按钮
                            Button(action: {
                                showForeground.toggle()
                            }) {
                                Text(showForeground ? "隐藏人物" : "显示人物")
                                    .font(Font.custom("PingFang SC", size: 14))
                                    .foregroundColor(.blue)
                            }
                        }
                        
                        // 添加缩放滑块
                        VStack(alignment: .leading, spacing: 8) {
                            
                            // 滑块和百分比输入放在同一行
                            HStack {
                                // 滑块轨道
                                ZStack(alignment: .center) {
                                    // 滑块轨道
                                    Rectangle()
                                        .fill(Color.gray.opacity(0.2))
                                        .frame(height: 4)
                                        .cornerRadius(2)
                                    
                                    // 滑块
                                    GeometryReader { geo in
                                        let width = geo.size.width
                                        let knobPosition = width * (foregroundScale - 0.01) / 2.0
                                        
                                        // 滑块
                                        Image("m-2")
                                            .resizable()
                                            .frame(width: 20, height: 20)
                                            .position(x: knobPosition, y: geo.size.height / 2)
                                            .gesture(
                                                DragGesture()
                                                    .onChanged { value in
                                                        let percentage = max(0, min(width, value.location.x)) / width
                                                        // 从0.01到2.01的范围
                                                        let newScale = 0.01 + percentage * 2
                                                        foregroundScale = newScale
                                                        updateBoundingBoxInfo()
                                                    }
                                            )
                                    }
                                    .frame(height: 30)
                                }
                                
                                // 百分比显示和输入
                                TextField("", text: Binding(
                                    get: { "\(Int((foregroundScale - 1) * 100))" },
                                    set: { newValue in
                                        if let value = Int(newValue), value >= -99, value <= 1000 {
                                            foregroundScale = 1 + Double(value) / 100
                                            updateBoundingBoxInfo()
                                        }
                                    }
                                ))
                                .keyboardType(.numberPad)
                                .frame(width: 40, alignment: .trailing)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .focused($isTextFieldFocused)
                                
                                Text("%")
                                    .font(.system(size: 14))
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.vertical, 5)
                        
                        // 功能按钮
                        HStack(spacing: 20) {
                            // 旋转按钮
                            Button(action: {
                                // 显示旋转轮盘
                                withAnimation {
                                    showRotationWheel.toggle()
                                }
                            }) {
                                HStack(spacing: 5) {
                                    Image("t-1") // 使用指定图标
                                        .resizable()
                                        .frame(width: 20, height: 20)
                                    Text("旋转")
                                        .font(Font.custom("PingFang SC", size: 15))
                                }
                                .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                                .frame(width: 91, height: 33)
                                .background(
                                    Rectangle()
                                        .foregroundColor(.clear)
                                        .frame(width: 91, height: 33)
                                        .background(.white)
                                        .cornerRadius(30)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 30)
                                                .inset(by: 0.25)
                                                .stroke(Color(red: 0.98, green: 0.85, blue: 0.37), lineWidth: 0.5)
                                        )
                                )
                            }
                            
                            // 翻转按钮
                            Button(action: {
                                // 翻转人物图
                                withAnimation {
                                    isFlipped.toggle()
                                }
                            }) {
                                HStack(spacing: 5) {
                                    Image("t-2") // 使用指定图标
                                        .resizable()
                                        .frame(width: 20, height: 20)
                                    Text("翻转")
                                        .font(Font.custom("PingFang SC", size: 15))
                                }
                                .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                                .frame(width: 91, height: 33)
                                .background(
                                    Rectangle()
                                        .foregroundColor(.clear)
                                        .frame(width: 91, height: 33)
                                        .background(.white)
                                        .cornerRadius(30)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 30)
                                                .inset(by: 0.25)
                                                .stroke(Color(red: 0.98, green: 0.85, blue: 0.37), lineWidth: 0.5)
                                        )
                                )
                            }
                            
                            // 生成图片按钮
                            Button(action: {
                                // 检查是否有前景图
                                guard let foregroundImg = foregroundImage else {
                                    // 如果没有前景图，显示提示
                                    return
                                }

                                // 生成前景图镜像翻转版本（如果需要）
                                let finalForegroundImage: UIImage
                                if isFlipped {
                                    // 使用Core Image进行可靠的水平翻转
                                    if let cgImage = foregroundImg.cgImage {
                                        let ciImage = CIImage(cgImage: cgImage)
                                        let flippedImage = ciImage.transformed(by: CGAffineTransform(scaleX: -1, y: 1))

                                        let ciContext = CIContext(options: nil)
                                        if let flippedCGImage = ciContext.createCGImage(flippedImage, from: flippedImage.extent) {
                                            finalForegroundImage = UIImage(cgImage: flippedCGImage, scale: foregroundImg.scale, orientation: foregroundImg.imageOrientation)
                                        } else {
                                            // 如果翻转失败，使用原始图像
                                            finalForegroundImage = foregroundImg
                                        }
                                    } else {
                                        finalForegroundImage = foregroundImg
                                    }
                                } else {
                                    finalForegroundImage = foregroundImg
                                }
                                
                                // 获取边界框信息
                                updateBoundingBoxInfo()
                                
                                
                                // 调用API提交图像处理任务
                                if let bg = backgroundImage {
                                    // 直接对原始背景图进行模糊处理
                                    var finalBackgroundImage = bg
                                    if blurAmount > 0 {
                                        // 重新生成模糊图像，保持原始分辨率
                                        finalBackgroundImage = getBlurredImage(image: bg, blurAmount: blurAmount)
                                    }
                                    
                                    // 显示加载中提示
                                    let loadingAlert = UIAlertController(title: "正在提交", message: "请稍候...", preferredStyle: .alert)
                                    
                                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                                       let rootVC = windowScene.windows.first?.rootViewController {
                                        var currentVC = rootVC
                                        while let presented = currentVC.presentedViewController {
                                            currentVC = presented
                                        }
                                        currentVC.present(loadingAlert, animated: true)
                                    }
                                    
                                   
                                    APIService.shared.submitImageProcessingTask(
                                        userId: UserState.shared.userId,
                                        foregroundImage: finalForegroundImage,
                                        backgroundImage: finalBackgroundImage, // 确保这里使用的是处理后的图像
                                        X: Int(boundingBoxTopLeft.x),
                                        Y: Int(boundingBoxTopLeft.y),
                                        Width: Int(boundingBoxSize.width),
                                        Height: Int(boundingBoxSize.height),
                                        Angle: Int(rotationAngle)
                                    ) { result in
                                        // 关闭加载提示
                                        DispatchQueue.main.async {
                                            loadingAlert.dismiss(animated: true) {
                                                switch result {
                                                case .success(let taskId):
                                                    
                                                    // 导航到处理结果页面
                                                    self.navigateToProcessingView(taskId: taskId)
                                                    
                                                case .failure(let error):
                                                    
                                                    
                                                    // 显示错误提示
                                                    let errorAlert = UIAlertController(title: "提交失败", message: error.localizedDescription, preferredStyle: .alert)
                                                    errorAlert.addAction(UIAlertAction(title: "确定", style: .default))
                                                    
                                                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                                                       let rootVC = windowScene.windows.first?.rootViewController {
                                                        var currentVC = rootVC
                                                        while let presented = currentVC.presentedViewController {
                                                            currentVC = presented
                                                        }
                                                        currentVC.present(errorAlert, animated: true)
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    // 显示错误提示：没有背景图
                                    let errorAlert = UIAlertController(title: "提交失败", message: "请先选择背景图", preferredStyle: .alert)
                                    errorAlert.addAction(UIAlertAction(title: "确定", style: .default))
                                    
                                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                                       let rootVC = windowScene.windows.first?.rootViewController {
                                        var currentVC = rootVC
                                        while let presented = currentVC.presentedViewController {
                                            currentVC = presented
                                        }
                                        currentVC.present(errorAlert, animated: true)
                                    }
                                }
                            }) {
                                HStack(spacing: 5) {
                                    Image("t-3") // 使用指定图标
                                        .resizable()
                                        .frame(width: 20, height: 20)
                                    Text("确认")
                                        .font(Font.custom("PingFang SC", size: 15))
                                }
                                .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                                .frame(width: 110, height: 33)
                                .background(
                                    Rectangle()
                                        .foregroundColor(.clear)
                                        .frame(width: 110, height: 33)
                                        .background(.white)
                                        .cornerRadius(30)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 30)
                                                .inset(by: 0.25)
                                                .stroke(Color(red: 0.98, green: 0.85, blue: 0.37), lineWidth: 0.5)
                                        )
                                )
                            }
                        }
                    }
                    .padding(.horizontal, 20)
                }
                .padding(.vertical, 20)
                .background(Color.white)
            }
            
            // 旋转轮盘
            if showRotationWheel {
                // 在底部显示轮盘组件，点击背景区域关闭轮盘
                ZStack {
                    // 半透明背景覆盖整个屏幕，点击关闭轮盘
                    Color.black.opacity(0.3)
                        .ignoresSafeArea()
                        .onTapGesture {
                            withAnimation {
                                showRotationWheel = false
                            }
                        }
                    
                    VStack {
                        Spacer()
                        
                        // 轮盘区域背景
                        Rectangle()
                            .fill(Color(red: 0.95, green: 0.95, blue: 0.97))
                            .frame(height: 250)
                            .overlay(
                                // 旋转轮盘视图
                                SemiCircleRotationWheelView(angle: $rotationAngle, showWheel: $showRotationWheel)
                                    .frame(height: 220)
                                    .padding(.horizontal, 10)
                            )
                    }
                    .ignoresSafeArea(edges: .bottom)
                }
                .transition(.move(edge: .bottom))
            }

            // 更换背景弹窗
            if showBackgroundSourcePicker {
                ImageSourcePickerSheet(
                    isPresented: $showBackgroundSourcePicker,
                    onTemplateSelected: {
                        showTemplateGallery = true
                        isSelectingBackground = true
                    },
                    onCameraSelected: {
                        showCamera = true
                        isSelectingBackground = true
                    },
                    onPhotoLibrarySelected: {
                        showImagePicker = true
                        isSelectingBackground = true
                    },
                    showTemplateOption: true
                )
            }

            // 选择人物弹窗
            if showCharacterSourcePicker {
                ImageSourcePickerSheet(
                    isPresented: $showCharacterSourcePicker,
                    onTemplateSelected: {
                        // 人物选择不显示模版选项，这里不会被调用
                    },
                    onCameraSelected: {
                        showCamera = true
                        isSelectingBackground = false
                    },
                    onPhotoLibrarySelected: {
                        showImagePicker = true
                        isSelectingBackground = false
                    },
                    showTemplateOption: false
                )
            }
        }
        .navigationBarHidden(true)
        .fullScreenCover(isPresented: $showTemplateGallery) {
            TemplateGalleryView { selectedTemplate in
                showTemplateGallery = false
                showBackgroundSourcePicker = false
                handleSelectedImage(selectedTemplate, isBackground: true)
            }
        }
        .fullScreenCover(isPresented: $showImagePicker) {
            ImagePickerView(sourceType: .photoLibrary) { image in
                // 先关闭图片选择器
                showImagePicker = false

                if let image = image {
                    self.selectedImage = image
                    // 延迟处理选择的图片，确保界面已关闭
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        self.handleSelectedImage(image, isBackground: self.isSelectingBackground)
                    }
                }
            }
        }
        .fullScreenCover(isPresented: $showCamera) {
            ImagePickerView(sourceType: .camera) { image in
                // 先关闭相机
                showCamera = false

                if let image = image {
                    self.selectedImage = image
                    // 延迟处理选择的图片，确保界面已关闭
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        self.handleSelectedImage(image, isBackground: self.isSelectingBackground)
                    }
                }
            }
        }
        .onAppear {
            // 检查是否从模板创作进入，如果是则加载模板背景
            if UserState.shared.isCreatingFromTemplate && !UserState.shared.templateBackgroundPath.isEmpty {
                loadTemplateBackground()
            }

            // 初始化显示模糊值
            displayBlurAmount = blurAmount

            // 如果有背景图且模糊值大于0，预先生成一次模糊效果
            if let _ = backgroundImage, blurAmount > 0 {
                applyBlurEffect(blurAmount: blurAmount)
            }
        }
        // 添加全局点击手势来关闭键盘
        .contentShape(Rectangle())
        .simultaneousGesture(
            TapGesture().onEnded {
                isTextFieldFocused = false
            }
        )
        // 人物裁剪界面
        .fullScreenCover(isPresented: $showCharacterCropping) {
            if let image = characterImageToCrop {
                CharacterCroppingView(
                    image: image,
                    onCropComplete: { croppedImage in
                        // 裁剪完成，用裁剪后的图片替换前景图片
                        showCharacterCropping = false
                        characterImageToCrop = nil
                        handleCropComplete(croppedImage)
                    },
                    onCancel: {
                        showCharacterCropping = false
                        characterImageToCrop = nil
                    }
                )
            } else {
                // 如果没有图片，显示空白页面并自动关闭
                Color.black
                    .onAppear {
                        showCharacterCropping = false
                    }
            }
        }
    }

    // 处理选择的图片
    private func handleSelectedImage(_ image: UIImage, isBackground: Bool) {
        if isBackground {
            // 更换背景图片，直接在当前页面更新背景
            updateBackgroundImage(image)
        } else {
            // 选择人物图片，进入内置裁剪模式
            startCharacterCropping(image: image)
        }
    }

    // 更新背景图片
    private func updateBackgroundImage(_ newBackground: UIImage) {
        // 保存当前前景图和变换参数（但不保存缩放值，因为需要重新计算）
        let currentForeground = foregroundImage
        let currentRotationAngle = rotationAngle
        let currentIsFlipped = isFlipped

        // 重置所有相关状态
        resetAllParameters()

        // 直接更新背景图片
        backgroundImage = newBackground

        // 重置背景显示信息，让GeometryReader重新计算
        backgroundDisplayInfo = DisplayInfo.zero

        // 如果有前景图，保存状态并标记需要重新初始化
        if let foreground = currentForeground {
            foregroundImage = foreground
            rotationAngle = currentRotationAngle
            isFlipped = currentIsFlipped
            // 注意：不恢复foregroundScale，让initializeForegroundDisplay重新计算
            showForeground = false  // 先隐藏前景图

            // 标记需要重新初始化前景图，等待GeometryReader重新计算背景显示信息
            needsReinitializeForeground = true
        }

        
    }

    // 计算背景图显示信息（使用与GeometryReader相同的逻辑）
    private func calculateBackgroundDisplayInfo(backgroundImg: UIImage, geometry: GeometryProxy) {
        let displayScale = min(
            geometry.size.width / backgroundImg.size.width,
            geometry.size.height / backgroundImg.size.height
        )

        let displayWidth = backgroundImg.size.width * displayScale
        let displayHeight = backgroundImg.size.height * displayScale

        backgroundDisplayInfo = DisplayInfo(
            scale: displayScale,
            size: CGSize(width: backgroundImg.size.width, height: backgroundImg.size.height),
            displaySize: CGSize(width: displayWidth, height: displayHeight),
            position: CGPoint(
                x: (geometry.size.width - displayWidth) / 2,
                y: (geometry.size.height - displayHeight) / 2
            )
        )
    }



    // 等待背景显示信息准备完成并初始化前景图
    private func waitForBackgroundDisplayInfoAndInitializeForeground(retryCount: Int = 0) {
        let maxRetries = 10 // 最多重试10次，总共约1秒
        if backgroundDisplayInfo.scale > 0 && backgroundDisplayInfo.displaySize.width > 0 && backgroundDisplayInfo.displaySize.height > 0 {
            // 背景显示信息已准备好
            self.initializeForegroundDisplayForCurrentGeometry()
        } else if retryCount < maxRetries {
            // 背景显示信息还没准备好，继续等待
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.waitForBackgroundDisplayInfoAndInitializeForeground(retryCount: retryCount + 1)
            }
        } else {
            // 超过最大重试次数，强制初始化
            self.initializeForegroundDisplayForCurrentGeometry()
        }
    }

    // 重置所有参数
    private func resetAllParameters() {
        // 重置背景模糊相关
        blurAmount = 0
        displayBlurAmount = 0
        blurredImageCache.removeAll()

        // 重置人物调节相关
        showForeground = true
        rotationAngle = 0
        showRotationWheel = false
        isFlipped = false

        // 重置人物位置和缩放
        foregroundOffset = .zero
        foregroundScale = 1.0
        lastScale = 1.0

        // 重置边界框信息和显示信息
        boundingBoxTopLeft = .zero
        boundingBoxSize = .zero
        backgroundDisplayInfo = .zero
        foregroundDisplayInfo = .zero

        // 重置其他UI状态
        showImagePicker = false
        showCamera = false
        showBackgroundSourcePicker = false
        showCharacterSourcePicker = false
        showTemplateGallery = false
        isSelectingBackground = false
        showCharacterCropping = false
        selectedImage = nil
        characterImageToCrop = nil

        // 重置模糊处理状态
        isProcessingBlur = false
        blurDebounceTimer?.invalidate()
        blurDebounceTimer = nil
    }

    // 处理裁剪完成
    private func handleCropComplete(_ croppedImage: UIImage) {
        // 打印日志信息
        let backgroundSize = backgroundImage?.size ?? CGSize.zero
        let croppedSize = croppedImage.size

        
        // 计算是否需要缩放
        var scaleFactor: CGFloat = 1.0
        if croppedSize.width > backgroundSize.width || croppedSize.height > backgroundSize.height {
            // 需要缩放到不超过背景图
            let widthScale = backgroundSize.width / croppedSize.width
            let heightScale = backgroundSize.height / croppedSize.height
            scaleFactor = min(widthScale, heightScale)
        } 

        // 更新前景图片和缩放值
        updateForegroundImageWithScale(croppedImage, scale: scaleFactor)

    }

    // 更新前景图片（带缩放值）
    private func updateForegroundImageWithScale(_ newForeground: UIImage, scale: CGFloat) {
        // 保存当前背景图片，避免被重置
        let currentBackground = backgroundImage
        let currentBackgroundDisplayInfo = backgroundDisplayInfo

        // 重置人物相关参数，但保留背景相关信息
        showForeground = true
        rotationAngle = 0
        showRotationWheel = false
        isFlipped = false
        foregroundOffset = .zero
        foregroundScale = scale  // 使用传入的缩放值
        lastScale = scale
        boundingBoxTopLeft = .zero
        boundingBoxSize = .zero
        foregroundDisplayInfo = .zero

        // 恢复背景图片和显示信息
        backgroundImage = currentBackground
        backgroundDisplayInfo = currentBackgroundDisplayInfo

        // 更新前景图片（不调整大小，保持原始尺寸）
        foregroundImage = newForeground

        // 重新初始化前景图显示信息
        DispatchQueue.main.async {
            // 如果backgroundDisplayInfo已经设置，重新初始化前景图显示
            if self.backgroundDisplayInfo.scale > 0 {
                self.initializeForegroundDisplayForCurrentGeometry()
            }
        }
    }

    // 更新前景图片
    private func updateForegroundImage(_ newForeground: UIImage) {
        updateForegroundImageWithScale(newForeground, scale: 1.0)
    }

    // 为当前几何信息初始化前景图显示 - 使用testcrop.swift的逻辑
    private func initializeForegroundDisplayForCurrentGeometry() {
        guard let foregroundImg = foregroundImage else { return }

        // 确保背景显示信息有效
        guard backgroundDisplayInfo.scale > 0 && backgroundDisplayInfo.displaySize.width > 0 && backgroundDisplayInfo.displaySize.height > 0 else {
            return
        }

        // 使用实际的背景显示信息
        let displayScale = backgroundDisplayInfo.scale
        let backgroundDisplaySize = backgroundDisplayInfo.displaySize
        let backgroundPosition = backgroundDisplayInfo.position


        // 确保背景显示尺寸有效
        if backgroundDisplaySize.width <= 0 || backgroundDisplaySize.height <= 0 {
            return
        }

        // 计算前景图的原始显示尺寸
        let originalDisplayWidth = foregroundImg.size.width * displayScale
        let originalDisplayHeight = foregroundImg.size.height * displayScale

        // 检查前景图是否超出背景图尺寸，如果超出则需要缩放
        var adjustedForegroundScale = foregroundScale

        if originalDisplayWidth > backgroundDisplaySize.width || originalDisplayHeight > backgroundDisplaySize.height {
            // 计算需要的缩放比例以适应背景图
            let widthScale = backgroundDisplaySize.width / originalDisplayWidth
            let heightScale = backgroundDisplaySize.height / originalDisplayHeight
            let requiredScale = min(widthScale, heightScale)

            // 更新前景图缩放值
            adjustedForegroundScale = requiredScale
            foregroundScale = adjustedForegroundScale

        }

        // 计算调整后的前景图显示尺寸
        let displayWidth = foregroundImg.size.width * displayScale * adjustedForegroundScale
        let displayHeight = foregroundImg.size.height * displayScale * adjustedForegroundScale

        // 计算前景图居中位置的偏移量（相对于背景图左上角）
        let offsetX = (backgroundDisplaySize.width - displayWidth) / 2
        let offsetY = (backgroundDisplaySize.height - displayHeight) / 2

        foregroundDisplayInfo = DisplayInfo(
            scale: displayScale,
            size: CGSize(width: foregroundImg.size.width, height: foregroundImg.size.height),
            displaySize: CGSize(width: foregroundImg.size.width * displayScale, height: foregroundImg.size.height * displayScale),
            position: backgroundPosition
        )

        // 设置偏移量，将前景图放置在背景图的中心
        // 注意：这里的偏移量是相对于背景图左上角的，不包含背景图在屏幕上的位置
        foregroundOffset = CGSize(width: offsetX, height: offsetY)

        // 初始化时计算边界框信息
        updateBoundingBoxInfo()
        
    }

    // 调整前景图片大小，确保不超过背景图片，并返回缩放比例
    private func adjustForegroundImageSizeWithScale(_ image: UIImage) -> (UIImage, CGFloat) {
        guard let background = backgroundImage else {
            return (image, 1.0)
        }

        let backgroundSize = background.size
        let foregroundSize = image.size

        // 计算缩放比例，确保前景图片不超过背景图片
        let widthRatio = backgroundSize.width / foregroundSize.width
        let heightRatio = backgroundSize.height / foregroundSize.height
        let scaleFactor = min(widthRatio, heightRatio, 1.0) // 最大不超过1.0，避免放大

        // 如果不需要缩放，直接返回原图
        if scaleFactor >= 1.0 {
            return (image, 1.0)
        }

        // 计算新的尺寸
        let newSize = CGSize(
            width: foregroundSize.width * scaleFactor,
            height: foregroundSize.height * scaleFactor
        )

        // 创建新的图片
        UIGraphicsBeginImageContextWithOptions(newSize, false, image.scale)
        image.draw(in: CGRect(origin: .zero, size: newSize))
        let resizedImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()

        return (resizedImage ?? image, scaleFactor)
    }

    // 调整前景图片大小，确保不超过背景图片（保留原方法用于兼容）
    private func adjustForegroundImageSize(_ image: UIImage) -> UIImage {
        let (adjustedImage, _) = adjustForegroundImageSizeWithScale(image)
        return adjustedImage
    }

    // 开始人物裁剪
    private func startCharacterCropping(image: UIImage) {
        // 先关闭所有其他弹窗
        showCharacterSourcePicker = false
        showImagePicker = false
        showCamera = false

        // 设置待裁剪的图片
        characterImageToCrop = image

        // 初始化裁剪框位置（居中）
        let imageAspectRatio = image.size.height / image.size.width
        let cropWidth: CGFloat = 200
        let cropHeight: CGFloat = cropWidth * imageAspectRatio

        cropFrameRect = CGRect(
            x: (UIScreen.main.bounds.width - cropWidth) / 2,
            y: (UIScreen.main.bounds.height - cropHeight) / 2,
            width: cropWidth,
            height: cropHeight
        )

        // 延迟显示裁剪界面，确保其他界面已关闭
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.showCharacterCropping = true
        }
    }

    // 导航到新的图像调整页面（更换背景）
    private func navigateToNewImageAdjustment(foreground: UIImage, background: UIImage) {
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {

            var currentController = rootViewController
            while let presented = currentController.presentedViewController {
                currentController = presented
            }

            // 先关闭当前页面，然后打开新页面
            currentController.dismiss(animated: false) {
                // 在关闭完成后，重新获取当前控制器并展示新页面
                var newCurrentController = rootViewController
                while let presented = newCurrentController.presentedViewController {
                    newCurrentController = presented
                }

                let hostingController = UIHostingController(rootView:
                    ImageAdjustmentView(
                        foregroundImage: foreground,
                        backgroundImage: background,
                        onBack: { [weak newCurrentController] in
                            newCurrentController?.dismiss(animated: true, completion: nil)
                        }
                    )
                )
                hostingController.modalPresentationStyle = .fullScreen
                newCurrentController.present(hostingController, animated: true)
            }
        }
    }



    // 初始化前景图显示信息 - 严格按照testcrop.swift的逻辑
    private func initializeForegroundDisplay(geometry: GeometryProxy) {
        guard let foregroundImg = foregroundImage else { return }
        guard backgroundDisplayInfo.scale > 0 else {
            return
        }

        
        // 使用与背景图相同的缩放比例
        let displayScale = backgroundDisplayInfo.scale

        // 计算前景图的显示尺寸
        let displayWidth = foregroundImg.size.width * displayScale
        let displayHeight = foregroundImg.size.height * displayScale

        // 检查前景图是否超过背景图的尺寸，如果是，则需要额外缩小
        var additionalScale: CGFloat = 1.0
        let backgroundOriginalWidth = backgroundImage?.size.width ?? 1.0
        let backgroundOriginalHeight = backgroundImage?.size.height ?? 1.0

        if foregroundImg.size.width > backgroundOriginalWidth || foregroundImg.size.height > backgroundOriginalHeight {
            // 计算需要额外缩小的比例
            let widthScale = backgroundOriginalWidth / foregroundImg.size.width
            let heightScale = backgroundOriginalHeight / foregroundImg.size.height
            additionalScale = min(widthScale, heightScale)
        } 

        // 更新前景图缩放比例（这是用户可调节的缩放值）
        foregroundScale = additionalScale

        // 应用额外缩放
        let finalDisplayWidth = displayWidth * additionalScale
        let finalDisplayHeight = displayHeight * additionalScale

        // 计算前景图居中位置的偏移量
        // 将边界框左上角坐标初始化为背景图中心减去前景图尺寸的一半
        let offsetX = (backgroundDisplayInfo.displaySize.width - finalDisplayWidth) / 2
        let offsetY = (backgroundDisplayInfo.displaySize.height - finalDisplayHeight) / 2

        foregroundDisplayInfo = DisplayInfo(
            scale: displayScale,
            size: CGSize(width: foregroundImg.size.width, height: foregroundImg.size.height),
            displaySize: CGSize(width: displayWidth, height: displayHeight),
            position: CGPoint(
                x: backgroundDisplayInfo.position.x,
                y: backgroundDisplayInfo.position.y
            )
        )

        // 设置偏移量，将前景图放置在背景图的中心
        foregroundOffset = CGSize(width: offsetX, height: offsetY)

        // 初始化时计算边界框信息
        updateBoundingBoxInfo()
    }
    
    // 更新边界框信息
    private func updateBoundingBoxInfo() {
        if backgroundDisplayInfo.scale > 0 {
            // 实际像素坐标 = 显示坐标 / 显示比例
            let topLeftX = foregroundOffset.width / backgroundDisplayInfo.scale
            let topLeftY = foregroundOffset.height / backgroundDisplayInfo.scale
            
            // 实际像素尺寸 = 显示尺寸 / 显示比例
            let actualWidth = (foregroundDisplayInfo.displaySize.width * foregroundScale) / backgroundDisplayInfo.scale
            let actualHeight = (foregroundDisplayInfo.displaySize.height * foregroundScale) / backgroundDisplayInfo.scale
            
            boundingBoxTopLeft = CGPoint(x: topLeftX, y: topLeftY)
            boundingBoxSize = CGSize(width: actualWidth, height: actualHeight)
        }
    }
    
    // 获取模糊处理后的图像 - 使用与后端处理相同的模糊算法
    private func getBlurredImage(image: UIImage, blurAmount: Double) -> UIImage {
        // 使用与发送到后端相同的方法进行模糊处理
        let ciContext = CIContext(options: nil)
        guard let cgImage = image.cgImage else {
            return image
        }
        
        let ciImage = CIImage(cgImage: cgImage)
        let filter = CIFilter(name: "CIGaussianBlur")!
        filter.setValue(ciImage, forKey: kCIInputImageKey)
        filter.setValue(blurAmount * BLUR_INTENSITY_MULTIPLIER, forKey: kCIInputRadiusKey)
        
        guard let outputCIImage = filter.outputImage else {
            return image
        }
        
        // 添加裁剪以避免模糊边缘扩展
        let cropRect = ciImage.extent
        guard let cgOutputImage = ciContext.createCGImage(outputCIImage, from: cropRect) else {
            return image
        }
        
        // 创建并返回模糊处理后的UIImage，保持原始分辨率和比例
        return UIImage(cgImage: cgOutputImage, scale: image.scale, orientation: image.imageOrientation)
    }
    
    // 导航到处理结果页面
    private func navigateToProcessingView(taskId: String) {
        
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            
            // 检查当前呈现的控制器，如果有，获取当前控制器
            var currentController = rootViewController
            while let presented = currentController.presentedViewController {
                currentController = presented
            }
            
            // 创建图像处理结果页面
            let hostingController = UIHostingController(rootView: 
                ImageProcessingView(
                    taskId: taskId,
                    onBack: { [weak currentController] in
                        // 返回到图像调节页面
                        currentController?.dismiss(animated: true, completion: nil)
                    }
                )
            )
            hostingController.modalPresentationStyle = UIModalPresentationStyle.fullScreen
            
            // 从当前控制器呈现处理结果页面
            currentController.present(hostingController, animated: true) {
            }
        } 
    }
    
    // 应用模糊效果的方法
    private func applyBlurEffect(blurAmount: Double) {
        guard let backgroundImg = backgroundImage else { return }
        
        // 避免重复处理
        if isProcessingBlur {
            return
        }
        
        // 标记正在处理
        isProcessingBlur = true
        
        // 在后台线程处理模糊效果
        DispatchQueue.global(qos: .userInitiated).async {
            // 对UI显示的图像进行降采样处理，减小处理的数据量
            let downsampledImage = downsampleImage(image: backgroundImg, to: CGSize(width: 800, height: 800))
            
            // 应用模糊效果
            let blurredImage = getBlurredImage(image: downsampledImage, blurAmount: blurAmount)
            
            // 更新显示，但不缓存
            DispatchQueue.main.async {
                // 为当前显示保存一个临时引用，不保留之前的缓存
                blurredImageCache.removeAll()
                blurredImageCache[blurAmount] = blurredImage
                displayBlurAmount = blurAmount
                isProcessingBlur = false
            }
        }
    }
    
    // 图像降采样方法
    private func downsampleImage(image: UIImage, to targetSize: CGSize) -> UIImage {
        let originalSize = image.size
        
        // 如果原图已经小于目标大小，直接返回
        if originalSize.width <= targetSize.width && originalSize.height <= targetSize.height {
            return image
        }
        
        // 计算缩放比例
        let widthRatio = targetSize.width / originalSize.width
        let heightRatio = targetSize.height / originalSize.height
        let scale = min(widthRatio, heightRatio)
        
        let newSize = CGSize(width: originalSize.width * scale, height: originalSize.height * scale)
        
        UIGraphicsBeginImageContextWithOptions(newSize, false, 0)
        image.draw(in: CGRect(origin: .zero, size: newSize))
        let downsampledImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return downsampledImage ?? image
    }
}

// 旋转轮盘视图
struct SemiCircleRotationWheelView: View {
    @Binding var angle: Double
    @Binding var showWheel: Bool
    @State private var isDraggingPointer = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 半圆形刻度轮盘
            ZStack {
                // 外圆轮廓 - 黄色
                Path { path in
                    path.addArc(center: CGPoint(x: 200, y: 120),
                                radius: 120,
                                startAngle: .degrees(180),
                                endAngle: .degrees(0),
                                clockwise: false)
                }
                .stroke(Color(red: 0.98, green: 0.85, blue: 0.37), lineWidth: 2)
                .frame(width: 400, height: 200)
                
                // 内圆轮廓 - 黄色
                Path { path in
                    path.addArc(center: CGPoint(x: 200, y: 120),
                                radius: 100,
                                startAngle: .degrees(180),
                                endAngle: .degrees(0),
                                clockwise: false)
                }
                .stroke(Color(red: 0.98, green: 0.85, blue: 0.37), lineWidth: 2)
                .frame(width: 400, height: 200)
                
                // 刻度线 - 每5度一个细刻度，每15度一个中刻度，只有-90到90度
                ForEach(-18..<19) { i in
                    let angle = Double(i) * 5
                    let isDivisibleBy9 = i % 9 == 0  // 每45度一个主刻度
                    let isDivisibleBy3 = i % 3 == 0  // 每15度一个中刻度
                    
                    if isDivisibleBy9 {
                        // 主刻度线 - 黑色粗线
                        Rectangle()
                            .fill(Color.black)
                            .frame(width: 3, height: 20)
                            .offset(y: -110)
                            .rotationEffect(.degrees(angle))
                            .position(x: 200, y: 120)
                    } else if isDivisibleBy3 {
                        // 中刻度线 - 黑色细线
                        Rectangle()
                            .fill(Color.black)
                            .frame(width: 2, height: 15)
                            .offset(y: -110)
                            .rotationEffect(.degrees(angle))
                            .position(x: 200, y: 120)
                    } else {
                        // 小刻度线 - 灰色细线
                        Rectangle()
                            .fill(Color(white: 0.6))
                            .frame(width: 1, height: 10)
                            .offset(y: -110)
                            .rotationEffect(.degrees(angle))
                            .position(x: 200, y: 120)
                    }
                }
                
                // 角度标签 - 只显示主要角度，放在圆环内侧
                ZStack {
                    // -90度
                    Text("-90°")
                        .font(.system(size: 14))
                        .fontWeight(.medium)
                        .foregroundColor(.black)
                        .position(x: 120, y: 120)
                    
                    // -45度
                    Text("-45°")
                        .font(.system(size: 14))
                        .fontWeight(.medium)
                        .foregroundColor(.black)
                        .position(x: 155, y: 55)
                    
                    // 0度
                    Text("0°")
                        .font(.system(size: 14))
                        .fontWeight(.medium)
                        .foregroundColor(.black)
                        .position(x: 200, y: 120)
                    
                    // 45度
                    Text("45°")
                        .font(.system(size: 14))
                        .fontWeight(.medium)
                        .foregroundColor(.black)
                        .position(x: 245, y: 55)
                    
                    // 90度
                    Text("90°")
                        .font(.system(size: 14))
                        .fontWeight(.medium)
                        .foregroundColor(.black)
                        .position(x: 290, y: 120)
                }
                
                // 指针 - 从圆心出发的半径指针
                Group {
                    // 指针主体 - 从圆心向外延伸的线段
                    Path { path in
                        // 从圆心(200, 120)出发
                        path.move(to: CGPoint(x: 200, y: 120))
                        // 向上延伸到指定位置
                        path.addLine(to: CGPoint(x: 200, y: 40))
                    }
                    .stroke(Color(red: 0.98, green: 0.85, blue: 0.37), lineWidth: 4)
                    
                    // 指针尖端 - 三角形
                    Path { path in
                        path.move(to: CGPoint(x: 200, y: 35))
                        path.addLine(to: CGPoint(x: 195, y: 45))
                        path.addLine(to: CGPoint(x: 205, y: 45))
                        path.closeSubpath()
                    }
                    .fill(Color(red: 0.98, green: 0.85, blue: 0.37))
                    
                    // 指针圆心
                    Circle()
                        .fill(Color(red: 0.98, green: 0.85, blue: 0.37))
                        .frame(width: 20, height: 20)
                        .position(x: 200, y: 120)
                }
                .rotationEffect(.degrees(angle)) // 使用默认的旋转中心点(.center)，即(200, 120)
                .gesture(
                    DragGesture(minimumDistance: 0)
                        .onChanged { value in
                            isDraggingPointer = true
                            
                            // 计算拖动角度，以(200, 120)为旋转中心
                            let center = CGPoint(x: 200, y: 120)
                            let dragLocation = value.location
                            let dx = dragLocation.x - center.x
                            let dy = dragLocation.y - center.y
                            
                            // 计算角度（弧度）
                            var newAngle = atan2(dx, -dy) * (180 / .pi)
                            
                            // 限制角度在 -90 到 90 之间
                            newAngle = max(-90, min(90, newAngle))
                            
                            // 更新角度
                            self.angle = newAngle
                        }
                        .onEnded { _ in
                            isDraggingPointer = false
                        }
                )
            }
            .frame(width: 400, height: 200)
            .padding(.vertical, 10)
            .contentShape(Rectangle()) // 确保整个区域可以接收手势
        }
    }
}

// 图片选择器视图（替代ImagePicker）
struct ImagePickerView: UIViewControllerRepresentable {
    @Environment(\.presentationMode) var presentationMode
    var sourceType: UIImagePickerController.SourceType
    var onImageSelected: (UIImage?) -> Void
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        
        // 检查权限
        if sourceType == .camera {
            if UIImagePickerController.isSourceTypeAvailable(.camera) {
                picker.sourceType = .camera
            } else {
                picker.sourceType = .photoLibrary
            }
        } else {
            picker.sourceType = .photoLibrary
        }
        
        picker.delegate = context.coordinator
        picker.allowsEditing = false
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UINavigationControllerDelegate, UIImagePickerControllerDelegate {
        let parent: ImagePickerView
        
        init(_ parent: ImagePickerView) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey: Any]) {
            // 获取原始图片
            if let originalImage = info[.originalImage] as? UIImage {
                // 修正图片方向，确保相机和相册选择的图片都使用相同的方向处理
                let fixedImage = fixImageOrientation(originalImage)
                parent.onImageSelected(fixedImage)
            } else {
                parent.onImageSelected(nil)
            }
            parent.presentationMode.wrappedValue.dismiss()
        }
        
        // 修正图片方向的辅助方法
        private func fixImageOrientation(_ image: UIImage) -> UIImage {
            // 如果图片方向已经是正确的，直接返回
            if image.imageOrientation == .up {
                return image
            }
            
            // 创建绘图上下文
            UIGraphicsBeginImageContextWithOptions(image.size, false, image.scale)
            image.draw(in: CGRect(origin: .zero, size: image.size))
            
            // 获取修正后的图片
            let normalizedImage = UIGraphicsGetImageFromCurrentImageContext()
            UIGraphicsEndImageContext()
            
            return normalizedImage ?? image
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.onImageSelected(nil)
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}
// 用于处理保存图片到相册的辅助类
class ImageSaveHelper: NSObject {
    var onSaveCompletion: ((String) -> Void)?
    @Published var showAlert = false
    @Published var alertMessage = ""
    
    func saveImageToAlbum(image: UIImage) {
        UIImageWriteToSavedPhotosAlbum(image, self, #selector(imageSaved(_:didFinishSavingWithError:contextInfo:)), nil)
    }
    
    @objc func imageSaved(_ image: UIImage, didFinishSavingWithError error: Error?, contextInfo: UnsafeRawPointer) {
        if let error = error {
            onSaveCompletion?("保存失败: \(error.localizedDescription)")
        } else {
            onSaveCompletion?("图片已保存到相册")
        }
    }
}

// 图像处理结果视图
struct ImageProcessingView: View {
    var taskId: String
    @State private var processingStatus: String = "processing" // processing, completed, failed
    @State private var outputImagePath: String = ""
    @State private var bgImagePath: String = ""
    @State private var outputImage: UIImage? = nil
    @State private var timer: Timer? = nil
    @State private var showAlert: Bool = false
    @State private var alertMessage: String = ""
    @State private var isImageEnhanced: Bool = false // 标记图片是否被增强
    var onBack: () -> Void
    
    // 用于处理保存图片的类
    private let imageHelper = ImageSaveHelper()
    
    init(taskId: String, onBack: @escaping () -> Void = {}) {
        self.taskId = taskId
        self.onBack = onBack
    }
    
    var body: some View {
        ZStack {
            // 背景颜色
            Color.white.ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 标题栏
                HStack {
                    // 移除返回按钮，替换为空白区域
                    Color.clear
                        .frame(width: 20, height: 20)
                        .padding(.leading, 16)
                    
                    Spacer()
                    
                    // 标题
                    Text(processingStatus == "completed" ? "创作完成" : "创作中")
                        .font(Font.custom("PingFang SC", size: 20).weight(.bold))
                        .foregroundColor(.black)
                    
                    Spacer()
                    
                    // 占位，保持标题居中
                    Color.clear
                        .frame(width: 20, height: 20)
                        .padding(.trailing, 16)
                }
                .padding(.top, 16)
                .padding(.bottom, 20)
                
                // 图片展示区域
                GeometryReader { geometry in
                    ZStack {
                        if processingStatus == "completed" && outputImage != nil {
                            // 显示处理完成的图片
                            Image(uiImage: outputImage!)
                                .resizable()
                                .scaledToFit()
                                .frame(width: geometry.size.width, height: geometry.size.height)
                        } else if processingStatus == "failed" {
                            // 显示处理失败文字
                            VStack {
                                Text("处理失败")
                                    .font(Font.custom("PingFang SC", size: 20).weight(.medium))
                                    .foregroundColor(.red)
                            }
                            .frame(width: geometry.size.width, height: geometry.size.height)
                        } else {
                            // 显示加载动画
                            VStack {
                                ProgressView()
                                    .scaleEffect(1.5)
                                    .padding(.bottom, 20)
                                
                                Text("正在处理图像...")
                                    .font(Font.custom("PingFang SC", size: 16))
                                    .foregroundColor(.gray)
                            }
                            .frame(width: geometry.size.width, height: geometry.size.height)
                        }
                    }
                }
                .clipped()
                
                // 使用共享的底部按钮组件
                CreationActionButtonsView(
                    processingStatus: processingStatus,
                    outputImage: outputImage,
                    taskId: taskId,
                    isImageEnhanced: isImageEnhanced,
                    imageHelper: imageHelper,
                    onPublish: {
                        navigateToPublishView()
                    },
                    onGoToHome: {
                        navigateToHomePage()
                    },
                    onLightEnhance: {
                        navigateToLightEnhancementView()
                    },
                    showAlert: $showAlert,
                    alertMessage: $alertMessage
                )
            }
        }
        .navigationBarHidden(true)
        .onDisappear {
            // 确保视图消失时停止计时器
            timer?.invalidate()
            timer = nil
        }
        .onAppear {
            // 初始化图片保存辅助类的回调
            imageHelper.onSaveCompletion = { message in
                alertMessage = message
                showAlert = true
            }
            
            // 如果任务仍在进行中或图片尚未加载，则开始轮询
            if processingStatus != "completed" || outputImage == nil {
                startPollingTaskStatus()
            }
        }
        .alert(isPresented: $showAlert) {
            Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
        }
    }
    
    // 开始轮询任务状态
    private func startPollingTaskStatus() {
        // 先立即检查一次状态
        checkTaskStatus()
        
        // 然后开始定时检查
        timer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { _ in
            checkTaskStatus()
        }
    }
    
    // 检查任务状态
    private func checkTaskStatus() {
        APIService.shared.getTaskStatus(taskId: taskId) { result in
            switch result {
            case .success(let statusInfo):
                let (status, outputPath, bgPath) = statusInfo
                
                DispatchQueue.main.async {
                    processingStatus = status
                    
                    if status == "completed" {
                        // 停止定时器
                        timer?.invalidate()
                        
                        // 保存路径信息
                        outputImagePath = outputPath
                        bgImagePath = bgPath
                        
                        // 加载输出图像
                        loadOutputImage(path: outputPath)
                    }
                }
                
            case .failure(_):
                DispatchQueue.main.async {
                    // 将状态设置为失败
                    processingStatus = "failed"
                    
                    // 停止定时器
                    timer?.invalidate()
                }
            }
        }
    }
    
    // 加载输出图像
    private func loadOutputImage(path: String) {
        // 提取文件名
        let filename = path.components(separatedBy: "/").last ?? path
        // 直接使用文件名构建完整URL
        let urlString = "\(APIService.shared.baseURL)/images/file/output/\(filename)"
        
        guard let url = URL(string: urlString) else {
            DispatchQueue.main.async {
                processingStatus = "failed"
            }
            return
        }
        
        URLSession.shared.dataTask(with: url) { data, response, error in
            if let _ = response as? HTTPURLResponse {
            }
            
            if let _ = error {
                DispatchQueue.main.async {
                    processingStatus = "failed"
                }
                return
            }
            
            guard let data = data, let image = UIImage(data: data) else {
                DispatchQueue.main.async {
                    processingStatus = "failed"
                }
                return
            }
            
            DispatchQueue.main.async {
                outputImage = image
                
                // 保存原始图像，用于补光
                UserState.shared.originalOutputImage = image
            }
        }.resume()
    }
    
    // 导航到发布作品页面
    private func navigateToPublishView() {
        guard let image = outputImage else { return }
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            
            // 检查当前呈现的控制器，如果有，获取当前控制器
            var currentController = rootViewController
            while let presented = currentController.presentedViewController {
                currentController = presented
            }
            
            // 创建发布作品页面
            let hostingController = UIHostingController(rootView: 
                PublishView(
                    image: image,
                    taskId: taskId,
                    isImageEnhanced: isImageEnhanced, // 传递状态
                    onBack: { [weak currentController] in
                        // 返回到创作完成页面
                        currentController?.dismiss(animated: true, completion: nil)
                    }
                )
            )
            hostingController.modalPresentationStyle = UIModalPresentationStyle.fullScreen
            
            // 从当前控制器呈现发布作品页面
            currentController.present(hostingController, animated: true)
        }
    }
    
    // 导航到社区页面（保持原有逻辑）
    private func navigateToHomePage() {
        // 停止定时器
        timer?.invalidate()
        
        // 根据处理状态设置悬浮球状态
        if processingStatus == "completed" {
            // 如果已完成，则不显示悬浮球
            UserState.shared.showFloatingBall = false
            UserState.shared.isCreationInProgress = false
            UserState.shared.isCreationComplete = true
            
            // 创作完成时，将图像保存到创作记录中（如果存在图像）
            if let image = outputImage {
                // 检查是否已经存在该图像的记录
                let exists = UserState.shared.checkIfImageExists(image: image)
                if !exists {
                    if UserState.shared.saveCreationImage(image: image) != nil {
                    }
                } 
            }
        } else {
            // 如果还在处理中，则显示悬浮球
            UserState.shared.showFloatingBall = true
            UserState.shared.isCreationInProgress = true
            UserState.shared.isCreationComplete = false
        }
        
        // 保存其他状态信息
        UserState.shared.taskId = self.taskId
        UserState.shared.processingStatus = processingStatus
        UserState.shared.outputImage = outputImage
        UserState.shared.outputImagePath = outputImagePath
        UserState.shared.bgImagePath = bgImagePath
        
        // 使用NotificationCenter发送通知，在TabView中切换到社区标签
        NotificationCenter.default.post(name: Notification.Name("SwitchToHomeTab"), object: nil)
        
        // 关闭所有模态视图，返回到主界面
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            rootViewController.dismiss(animated: true, completion: nil)
        }
    }
    
    // 导航到人物补光页面
    private func navigateToLightEnhancementView() {
        // 确保有原始图像可用
        guard let originalImage = UserState.shared.originalOutputImage ?? outputImage else { return }
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            
            // 检查当前呈现的控制器，如果有，获取当前控制器
            var currentController = rootViewController
            while let presented = currentController.presentedViewController {
                currentController = presented
            }
            
            // 创建人物补光页面
            let hostingController = UIHostingController(rootView: 
                LightEnhancementView(
                    image: originalImage, // 使用原始图像
                    taskId: taskId,
                    onBack: { [weak currentController] in
                        // 返回到创作完成页面
                        currentController?.dismiss(animated: true, completion: nil)
                    },
                    onSave: { [weak currentController] enhancedImage in
                        // 保存增强后的图片并返回
                        if let controller = currentController {
                            // 更新当前页面的图像
                            DispatchQueue.main.async {
                                self.outputImage = enhancedImage
                                self.isImageEnhanced = true // 标记图片已被修改
                                
                                // 检查创作记录并更新
                                if UserState.shared.checkIfImageExists(image: originalImage) {
                                    _ = UserState.shared.updateCreationImage(originalImage: originalImage, newImage: enhancedImage)
                                }
                            }
                            controller.dismiss(animated: true, completion: nil)
                        }
                    }
                )
            )
            hostingController.modalPresentationStyle = UIModalPresentationStyle.fullScreen
            
            // 从当前控制器呈现人物补光页面
            currentController.present(hostingController, animated: true)
        }
    }
}

// 人物裁剪视图 - 使用testcrop.swift的设计
struct CharacterCroppingView: View {
    let image: UIImage
    let onCropComplete: (UIImage) -> Void
    let onCancel: () -> Void

    // 蓝色矩形框数据
    @State private var frameRect: CGRect = .zero
    @State private var isDragging = false
    @State private var dragMode: DragMode = .none
    @State private var startLocation: CGPoint = .zero
    @State private var imageSize: CGSize = .zero

    // 用于计算图片与视图大小的关系
    @State private var imageFrame: CGRect = .zero

    // 添加一个状态变量，用于在图片更新后强制重新初始化裁剪框
    @State private var imageUpdateCounter: Int = 0

    // 拖动模式
    enum DragMode {
        case none, move, resizeLeft, resizeRight, resizeTop, resizeBottom, resizeTopLeft, resizeTopRight, resizeBottomLeft, resizeBottomRight
    }

    var body: some View {
        ZStack {
            // 背景颜色
            Color.white.ignoresSafeArea()

            VStack(spacing: 0) {
                // 标题栏
                HStack {
                    // 返回按钮
                    Button(action: {
                        onCancel()
                    }) {
                        Image(systemName: "arrow.left")
                            .font(.system(size: 18))
                            .foregroundColor(.black)
                    }
                    .padding(.leading, 16)

                    Spacer()

                    // 标题
                    Text("人物选择")
                        .font(Font.custom("PingFang SC", size: 20).weight(.bold))
                        .foregroundColor(.black)

                    Spacer()

                    // 占位，保持标题居中
                    Color.clear
                        .frame(width: 20, height: 20)
                        .padding(.trailing, 16)
                }
                .padding(.top, 16)
                .padding(.bottom, 20)

                // 图片展示和选择区域
                GeometryReader { geometry in
                    ZStack {
                        // 图片
                        Image(uiImage: image)
                            .resizable()
                            .scaledToFit()
                            .frame(width: geometry.size.width, height: geometry.size.height)
                            .overlay(
                                GeometryReader { imageGeo in
                                    Color.clear
                                        .onAppear {
                                            // 记录图片实际大小
                                            self.imageFrame = imageGeo.frame(in: .global)

                                            // 初始化蓝色矩形框
                                            if self.frameRect == .zero {
                                                initializeSelectionFrame(imageGeo: imageGeo)
                                            }
                                        }
                                        // 添加id修饰符，当imageUpdateCounter变化时强制重新计算
                                        .id("imageOverlay_\(imageUpdateCounter)")
                                }
                            )

                        // 蓝色矩形框
                        SelectionRectangleView(
                            frameRect: $frameRect,
                            isDragging: $isDragging
                        )
                        .frame(width: geometry.size.width, height: geometry.size.height)
                    }
                    .gesture(
                        DragGesture()
                            .onChanged { value in
                                isDragging = true

                                if startLocation == .zero {
                                    startLocation = value.location

                                    // 确定拖动模式
                                    let touchPoint = value.location
                                    let edgeThreshold: CGFloat = 30

                                    // 检查是否在边缘或角落
                                    let nearLeft = abs(touchPoint.x - frameRect.minX) < edgeThreshold
                                    let nearRight = abs(touchPoint.x - frameRect.maxX) < edgeThreshold
                                    let nearTop = abs(touchPoint.y - frameRect.minY) < edgeThreshold
                                    let nearBottom = abs(touchPoint.y - frameRect.maxY) < edgeThreshold

                                    // 检查四个角落
                                    if nearLeft && nearTop {
                                        dragMode = .resizeTopLeft
                                    } else if nearRight && nearTop {
                                        dragMode = .resizeTopRight
                                    } else if nearLeft && nearBottom {
                                        dragMode = .resizeBottomLeft
                                    } else if nearRight && nearBottom {
                                        dragMode = .resizeBottomRight
                                    }
                                    // 检查四条边
                                    else if nearLeft {
                                        dragMode = .resizeLeft
                                    } else if nearRight {
                                        dragMode = .resizeRight
                                    } else if nearTop {
                                        dragMode = .resizeTop
                                    } else if nearBottom {
                                        dragMode = .resizeBottom
                                    } else if frameRect.contains(touchPoint) {
                                        dragMode = .move
                                    } else {
                                        dragMode = .none
                                    }

                                    return
                                }

                                let xOffset = value.location.x - startLocation.x
                                let yOffset = value.location.y - startLocation.y

                                // 最小尺寸限制
                                let minSize: CGFloat = 100

                                switch dragMode {
                                case .move:
                                    // 移动整个框
                                    var newRect = frameRect
                                    newRect.origin.x += xOffset
                                    newRect.origin.y += yOffset

                                    // 确保框不超出图片边界
                                    newRect.origin.x = max(0, min(newRect.origin.x, imageSize.width - newRect.width))
                                    newRect.origin.y = max(0, min(newRect.origin.y, imageSize.height - newRect.height))

                                    frameRect = newRect
                                    startLocation = value.location

                                case .resizeLeft:
                                    // 调整左边缘
                                    var newRect = frameRect
                                    newRect.origin.x += xOffset
                                    newRect.size.width -= xOffset

                                    // 确保不超出图片边界且保持最小尺寸
                                    if newRect.origin.x >= 0 && newRect.size.width >= minSize {
                                        frameRect = newRect
                                        startLocation = value.location
                                    }

                                case .resizeRight:
                                    // 调整右边缘
                                    var newRect = frameRect
                                    newRect.size.width += xOffset

                                    // 确保不超出图片边界且保持最小尺寸
                                    if newRect.maxX <= imageSize.width && newRect.size.width >= minSize {
                                        frameRect = newRect
                                        startLocation = value.location
                                    }

                                case .resizeTop:
                                    // 调整上边缘
                                    var newRect = frameRect
                                    newRect.origin.y += yOffset
                                    newRect.size.height -= yOffset

                                    // 确保不超出图片边界且保持最小尺寸
                                    if newRect.origin.y >= 0 && newRect.size.height >= minSize {
                                        frameRect = newRect
                                        startLocation = value.location
                                    }

                                case .resizeBottom:
                                    // 调整下边缘
                                    var newRect = frameRect
                                    newRect.size.height += yOffset

                                    // 确保不超出图片边界且保持最小尺寸
                                    if newRect.maxY <= imageSize.height && newRect.size.height >= minSize {
                                        frameRect = newRect
                                        startLocation = value.location
                                    }

                                case .resizeTopLeft:
                                    // 同时调整左边缘和上边缘
                                    var newRect = frameRect
                                    newRect.origin.x += xOffset
                                    newRect.origin.y += yOffset
                                    newRect.size.width -= xOffset
                                    newRect.size.height -= yOffset

                                    // 确保不超出图片边界且保持最小尺寸
                                    if newRect.origin.x >= 0 && newRect.origin.y >= 0 &&
                                       newRect.size.width >= minSize && newRect.size.height >= minSize {
                                        frameRect = newRect
                                        startLocation = value.location
                                    }

                                case .resizeTopRight:
                                    // 同时调整右边缘和上边缘
                                    var newRect = frameRect
                                    newRect.origin.y += yOffset
                                    newRect.size.width += xOffset
                                    newRect.size.height -= yOffset

                                    // 确保不超出图片边界且保持最小尺寸
                                    if newRect.origin.y >= 0 && newRect.maxX <= imageSize.width &&
                                       newRect.size.width >= minSize && newRect.size.height >= minSize {
                                        frameRect = newRect
                                        startLocation = value.location
                                    }

                                case .resizeBottomLeft:
                                    // 同时调整左边缘和下边缘
                                    var newRect = frameRect
                                    newRect.origin.x += xOffset
                                    newRect.size.width -= xOffset
                                    newRect.size.height += yOffset

                                    // 确保不超出图片边界且保持最小尺寸
                                    if newRect.origin.x >= 0 && newRect.maxY <= imageSize.height &&
                                       newRect.size.width >= minSize && newRect.size.height >= minSize {
                                        frameRect = newRect
                                        startLocation = value.location
                                    }

                                case .resizeBottomRight:
                                    // 同时调整右边缘和下边缘
                                    var newRect = frameRect
                                    newRect.size.width += xOffset
                                    newRect.size.height += yOffset

                                    // 确保不超出图片边界且保持最小尺寸
                                    if newRect.maxX <= imageSize.width && newRect.maxY <= imageSize.height &&
                                       newRect.size.width >= minSize && newRect.size.height >= minSize {
                                        frameRect = newRect
                                        startLocation = value.location
                                    }

                                case .none:
                                    break
                                }
                            }
                            .onEnded { _ in
                                startLocation = .zero
                                isDragging = false
                                dragMode = .none
                            }
                    )
                }
                .padding(.bottom, 20)

                // 底部按钮
                HStack(spacing: 30) {
                    // 取消按钮
                    Button(action: {
                        onCancel()
                    }) {
                        HStack(spacing: 5) {
                            Text("取消")
                                .font(Font.custom("PingFang SC", size: 15))
                        }
                        .foregroundColor(Color(red: 0.27, green: 0.18, blue: 0.13))
                        .frame(width: 91, height: 33)
                        .background(
                            Rectangle()
                                .foregroundColor(.clear)
                                .background(Color.white)
                                .cornerRadius(16.5)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 16.5)
                                        .inset(by: 0.5)
                                        .stroke(Color(red: 0.27, green: 0.18, blue: 0.13), lineWidth: 1)
                                )
                        )
                    }

                    // 确认按钮
                    Button(action: {
                        cropImage()
                    }) {
                        HStack(spacing: 5) {
                            Text("确认")
                                .font(Font.custom("PingFang SC", size: 15))
                        }
                        .foregroundColor(.white)
                        .frame(width: 91, height: 33)
                        .background(
                            Rectangle()
                                .foregroundColor(.clear)
                                .background(Color(red: 0.27, green: 0.18, blue: 0.13))
                                .cornerRadius(16.5)
                        )
                    }
                }
                .padding(.bottom, 30)
            }
        }
        .navigationBarHidden(true)
    }

    // 初始化选择框
    private func initializeSelectionFrame(imageGeo: GeometryProxy) {
        // 获取图片在视图中的实际显示尺寸
        let imageDisplayFrame = imageGeo.frame(in: .local)
        imageSize = imageDisplayFrame.size

        // 初始化蓝色矩形框，设置为图片中心的一个合适大小
        let frameWidth = min(imageSize.width * 0.6, 200)
        let frameHeight = min(imageSize.height * 0.6, 200)

        frameRect = CGRect(
            x: (imageSize.width - frameWidth) / 2,
            y: (imageSize.height - frameHeight) / 2,
            width: frameWidth,
            height: frameHeight
        )
    }

    private func cropImage() {
        // 使用testcrop.swift的裁剪方法
        if let croppedImage = cropImage(image: image, to: frameRect, imageSize: imageSize) {
            onCropComplete(croppedImage)
        } else {
            // 如果裁剪失败，返回原图
            onCropComplete(image)
        }
    }

    // 使用testcrop.swift的裁剪图片方法
    private func cropImage(image: UIImage, to rect: CGRect, imageSize: CGSize) -> UIImage? {
        let scale = min(
            imageSize.width / image.size.width,
            imageSize.height / image.size.height
        )

        let imageWidth = image.size.width * scale
        let imageHeight = image.size.height * scale

        let xOffset = (imageSize.width - imageWidth) / 2
        let yOffset = (imageSize.height - imageHeight) / 2

        let scaledRect = CGRect(
            x: (rect.origin.x - xOffset) / imageWidth * image.size.width,
            y: (rect.origin.y - yOffset) / imageHeight * image.size.height,
            width: rect.size.width / imageWidth * image.size.width,
            height: rect.size.height / imageHeight * image.size.height
        )

        let validRect = CGRect(
            x: max(0, min(scaledRect.origin.x, image.size.width)),
            y: max(0, min(scaledRect.origin.y, image.size.height)),
            width: min(image.size.width - scaledRect.origin.x, scaledRect.size.width),
            height: min(image.size.height - scaledRect.origin.y, scaledRect.size.height)
        )

        guard let cgImage = image.cgImage?.cropping(to: validRect) else {
            return nil
        }

        return UIImage(cgImage: cgImage, scale: image.scale, orientation: image.imageOrientation)
    }
}

// 选择矩形框视图 - 使用testcrop.swift的设计
struct SelectionRectangleView: View {
    @Binding var frameRect: CGRect
    @Binding var isDragging: Bool

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 顶部半透明遮罩
                Path { path in
                    path.addRect(CGRect(x: 0, y: 0, width: geometry.size.width, height: frameRect.minY))
                }
                .fill(Color.black.opacity(0.5))

                // 底部半透明遮罩
                Path { path in
                    path.addRect(CGRect(x: 0, y: frameRect.maxY, width: geometry.size.width, height: geometry.size.height - frameRect.maxY))
                }
                .fill(Color.black.opacity(0.5))

                // 左侧半透明遮罩
                Path { path in
                    path.addRect(CGRect(x: 0, y: frameRect.minY, width: frameRect.minX, height: frameRect.height))
                }
                .fill(Color.black.opacity(0.5))

                // 右侧半透明遮罩
                Path { path in
                    path.addRect(CGRect(x: frameRect.maxX, y: frameRect.minY, width: geometry.size.width - frameRect.maxX, height: frameRect.height))
                }
                .fill(Color.black.opacity(0.5))

                // 选择框
                Rectangle()
                    .stroke(Color.blue, lineWidth: isDragging ? 2 : 1)
                    .frame(
                        width: frameRect.width,
                        height: frameRect.height
                    )
                    .position(
                        x: frameRect.midX,
                        y: frameRect.midY
                    )

                // 左上角控制点
                Circle()
                    .fill(Color.white)
                    .frame(width: 14, height: 14)
                    .overlay(Circle().stroke(Color.blue, lineWidth: 2))
                    .position(x: frameRect.minX, y: frameRect.minY)

                // 右上角控制点
                Circle()
                    .fill(Color.white)
                    .frame(width: 14, height: 14)
                    .overlay(Circle().stroke(Color.blue, lineWidth: 2))
                    .position(x: frameRect.maxX, y: frameRect.minY)

                // 左下角控制点
                Circle()
                    .fill(Color.white)
                    .frame(width: 14, height: 14)
                    .overlay(Circle().stroke(Color.blue, lineWidth: 2))
                    .position(x: frameRect.minX, y: frameRect.maxY)

                // 右下角控制点
                Circle()
                    .fill(Color.white)
                    .frame(width: 14, height: 14)
                    .overlay(Circle().stroke(Color.blue, lineWidth: 2))
                    .position(x: frameRect.maxX, y: frameRect.maxY)
            }
        }
    }
}



