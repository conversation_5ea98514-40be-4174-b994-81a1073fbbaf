import SwiftUI
import Vision
import CoreImage
import CoreImage.CIFilterBuiltins
import PhotosUI

struct StyleStickerView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var userState: UserState

    // 图片相关状态
    @State private var selectedImage: UIImage?
    @State private var originalImage: UIImage?
    @State private var processedImage: UIImage?
    @State private var segmentationMask: CVPixelBuffer?
    @State private var showImagePicker = false
    @State private var selectedPhotoItem: PhotosPickerItem?

    // 风格转换相关
    @State private var selectedStyle: ONNXStyleTransfer.StyleType = .hayao
    @State private var isProcessingStyle = false
    @State private var styleTransferProgress: Double = 0.0
    @State private var styledImage: UIImage? // 保存原始风格转换结果
    private let onnxProcessor = ONNXStyleTransfer()

    // 人体检测相关
    @State private var isDetectingPerson = false
    @State private var personDetected = false

    // UI控制状态
    @State private var showBackground = true
    @State private var showStickerBorder = false
    @State private var beforeAfterPosition: CGFloat = 0.5
    @State private var showAlert = false
    @State private var alertMessage = ""

    // 处理状态
    @State private var isProcessing = false

    var body: some View {
        NavigationView {
            ZStack {
                Color.white.ignoresSafeArea()

                VStack(spacing: 20) {
                    // 顶部导航栏
                    HStack {
                        Button("取消") {
                            presentationMode.wrappedValue.dismiss()
                        }
                        .foregroundColor(.black)

                        Spacer()

                        Text("风格贴纸")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.black)

                        Spacer()

                        Button("保存") {
                            saveCurrentImage()
                        }
                        .foregroundColor(.black)
                        .disabled(processedImage == nil)
                    }
                    .padding(.horizontal)
                    .padding(.top, 10)

                    ScrollView {
                        VStack(spacing: 20) {
                            // 图片显示区域
                            imageDisplaySection

                            // 风格选择区域
                            if selectedImage != nil {
                                styleSelectionSection
                            }

                            // 控制按钮区域 - 在人体检测完成后就可以使用
                            if personDetected {
                                controlButtonsSection
                            }

                            // 处理按钮
                            if selectedImage != nil && personDetected {
                                processButtonSection
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.bottom, 30)
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .photosPicker(isPresented: $showImagePicker, selection: $selectedPhotoItem, matching: .images)
        .onChange(of: selectedPhotoItem) { _, newItem in
            Task {
                if let data = try? await newItem?.loadTransferable(type: Data.self),
                   let image = UIImage(data: data) {
                    await MainActor.run {
                        self.selectedImage = image
                        self.originalImage = image
                        self.processedImage = nil
                        self.styledImage = nil
                        self.segmentationMask = nil
                        self.personDetected = false
                        self.showBackground = true
                        self.showStickerBorder = false
                        self.detectPerson(image)
                    }
                }
            }
        }
        .alert(isPresented: $showAlert) {
            Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
        }
    }

    // MARK: - 图片显示区域
    private var imageDisplaySection: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 15)
                .fill(Color.gray.opacity(0.2))
                .frame(height: 400)

            if let processed = processedImage, let original = originalImage {
                // 显示前后对比
                BeforeAfterSlider(
                    beforeImage: original,
                    afterImage: processed,
                    sliderPosition: $beforeAfterPosition
                )
                .frame(maxHeight: 380)
                .cornerRadius(15)
            } else if let image = selectedImage {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxHeight: 380)
                    .cornerRadius(15)
            } else {
                VStack(spacing: 15) {
                    Image(systemName: "photo.badge.plus")
                        .font(.system(size: 50))
                        .foregroundColor(.gray)

                    Text("点击上传图片")
                        .font(.headline)
                        .foregroundColor(.gray)
                }
            }

            // 处理中的加载指示器
            if isProcessing || isDetectingPerson || isProcessingStyle {
                VStack(spacing: 10) {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.5)

                    Text(getProcessingText())
                        .foregroundColor(.white)
                        .font(.caption)
                }
                .padding()
                .background(Color.black.opacity(0.7))
                .cornerRadius(10)
            }
        }
        .onTapGesture {
            if !isProcessing && !isDetectingPerson && !isProcessingStyle {
                showImagePicker = true
            }
        }
    }

    // MARK: - 风格选择区域
    private var styleSelectionSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("选择风格")
                .font(Font.custom("PingFang SC", size: 18).weight(.bold))
                .foregroundColor(.black)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(ONNXStyleTransfer.StyleType.allCases, id: \.self) { style in
                    StyleOptionCard(
                        style: style,
                        isSelected: selectedStyle == style,
                        onTap: {
                            selectedStyle = style
                            // 不清除之前的结果，只有点击"开始风格转换"才更换结果
                        }
                    )
                }
            }
        }
    }

    // MARK: - 控制按钮区域
    private var controlButtonsSection: some View {
        VStack(spacing: 16) {
            HStack(spacing: 20) {
                Button(action: {
                    showBackground.toggle()
                    updateProcessedImage()
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: showBackground ? "eye.fill" : "eye.slash.fill")
                        Text(showBackground ? "隐藏背景" : "显示背景")
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(Color.blue)
                    .cornerRadius(20)
                }

                Button(action: {
                    showStickerBorder.toggle()
                    updateProcessedImage()
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: showStickerBorder ? "eye.slash.fill" : "eye.fill")
                        Text(showStickerBorder ? "隐藏边缘" : "显示边缘")
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(Color.purple)
                    .cornerRadius(20)
                }
            }


        }
    }

    // MARK: - 处理按钮区域
    private var processButtonSection: some View {
        Button(action: {
            processStyleTransfer()
        }) {
            HStack {
                if isProcessingStyle {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                    Text("处理中...")
                } else {
                    Image(systemName: "paintbrush.fill")
                    Text("开始风格转换")
                }
            }
            .font(Font.custom("PingFang SC", size: 16).weight(.medium))
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                LinearGradient(
                    colors: isProcessingStyle ? [Color.gray] : [Color.blue, Color.purple],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(25)
            .shadow(color: Color.blue.opacity(0.3), radius: 5, x: 0, y: 3)
        }
        .disabled(isProcessingStyle)
    }

    // MARK: - 核心功能方法

    // 人体检测
    private func detectPerson(_ image: UIImage) {
        guard !isDetectingPerson else { return }

        isDetectingPerson = true

        Task {
            do {
                let mask = try await performPersonSegmentation(image: image)
                await MainActor.run {
                    self.segmentationMask = mask
                    self.personDetected = mask != nil
                    self.isDetectingPerson = false
                }
            } catch {
                await MainActor.run {
                    self.personDetected = false
                    self.isDetectingPerson = false
                    self.alertMessage = "人体检测失败: \(error.localizedDescription)"
                    self.showAlert = true
                }
            }
        }
    }

    // 执行人体分割
    private func performPersonSegmentation(image: UIImage) async throws -> CVPixelBuffer? {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                let segmentationRequest = VNGenerateForegroundInstanceMaskRequest { request, error in
                    if error != nil {
                        continuation.resume(returning: nil)
                        return
                    }

                    guard let observation = request.results?.first as? VNInstanceMaskObservation else {
                        continuation.resume(returning: nil)
                        return
                    }

                    // 直接返回instanceMask的CVPixelBuffer
                    continuation.resume(returning: observation.instanceMask)
                }

                guard let cgImage = image.cgImage else {
                    continuation.resume(returning: nil)
                    return
                }

                let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])

                do {
                    try handler.perform([segmentationRequest])
                } catch {
                    continuation.resume(returning: nil)
                }
            }
        }
    }

    // 风格转换处理
    private func processStyleTransfer() {
        guard let image = selectedImage, !isProcessingStyle else { return }

        isProcessingStyle = true
        styleTransferProgress = 0.0

        Task {
            let styledImage = await withCheckedContinuation { continuation in
                DispatchQueue.global(qos: .userInitiated).async {
                    let result = self.onnxProcessor.processImage(image, styleType: self.selectedStyle)
                    continuation.resume(returning: result)
                }
            }

            await MainActor.run {
                if let styledImage = styledImage {
                    self.styledImage = styledImage
                    self.isProcessingStyle = false
                    self.updateProcessedImage()
                } else {
                    self.isProcessingStyle = false
                    self.alertMessage = "风格转换失败"
                    self.showAlert = true
                }
            }
        }
    }

    // 更新处理后的图片
    private func updateProcessedImage() {
        guard let mask = segmentationMask else { return }

        // 确定基础图片：如果有风格转换结果就用风格转换结果，否则用原图
        let baseImage = styledImage ?? originalImage
        guard let baseImg = baseImage else { return }

        var finalImage = baseImg

        // 如果需要隐藏背景
        if !showBackground {
            finalImage = applyBackgroundRemoval(to: finalImage, mask: mask) ?? finalImage
        }

        // 如果需要显示贴纸边缘
        if showStickerBorder {
            finalImage = applyStickerBorder(to: finalImage, mask: mask) ?? finalImage
        }

        // 更新显示的图片
        self.processedImage = finalImage
    }

    // 应用背景移除
    private func applyBackgroundRemoval(to image: UIImage, mask: CVPixelBuffer) -> UIImage? {
        guard let ciImage = CIImage(image: image) else { return nil }

        let maskImage = CIImage(cvPixelBuffer: mask)

        // 调整mask尺寸以匹配原图
        let scaledMask = maskImage.transformed(by: CGAffineTransform(
            scaleX: ciImage.extent.width / maskImage.extent.width,
            y: ciImage.extent.height / maskImage.extent.height
        ))

        // 转换为二值掩码
        let binaryMask = scaledMask.applyingFilter("CIColorMatrix", parameters: [
            "inputRVector": CIVector(x: 1000, y: 1000, z: 1000, w: 0),
            "inputGVector": CIVector(x: 1000, y: 1000, z: 1000, w: 0),
            "inputBVector": CIVector(x: 1000, y: 1000, z: 1000, w: 0),
            "inputAVector": CIVector(x: 0, y: 0, z: 0, w: 1)
        ]).applyingFilter("CIColorClamp", parameters: [
            "inputMinComponents": CIVector(x: 0, y: 0, z: 0, w: 0),
            "inputMaxComponents": CIVector(x: 1, y: 1, z: 1, w: 1)
        ])

        // 生成固定的随机坐标点用于对比
        let context = CIContext()
        let extent = ciImage.extent
        let width = Int(extent.width)
        let height = Int(extent.height)

        // 生成5个固定的随机坐标点
        let testPoints = (0..<5).map { _ in
            (x: Int.random(in: 0..<width), y: Int.random(in: 0..<height))
        }

        // 记录原图像的像素值用于对比
        logPixelValues(ciImage: ciImage, context: context, testPoints: testPoints, label: "隐藏背景前")

        // 记录掩码的像素值用于调试
        logPixelValues(ciImage: binaryMask, context: context, testPoints: testPoints, label: "二值掩码")

        // 直接基于掩码值处理像素：掩码RGB为0的像素设为透明，其余保持不变
        let maskedImage = applyDirectMasking(originalImage: ciImage, mask: binaryMask)

        // 记录处理后图像的像素值用于对比（使用相同的坐标点）
        logPixelValues(ciImage: maskedImage, context: context, testPoints: testPoints, label: "隐藏背景后")

        guard let cgImage = context.createCGImage(maskedImage, from: maskedImage.extent) else { return nil }

        return UIImage(cgImage: cgImage)
    }

    // 直接基于掩码值处理像素：掩码RGB为0的像素设为透明，其余保持不变
    private func applyDirectMasking(originalImage: CIImage, mask: CIImage) -> CIImage {
        let context = CIContext()

        // 获取图像尺寸
        let extent = originalImage.extent
        let width = Int(extent.width)
        let height = Int(extent.height)

        // 创建原始图像的CGImage
        guard let originalCGImage = context.createCGImage(originalImage, from: extent) else {
            return originalImage
        }

        // 创建掩码的CGImage
        guard let maskCGImage = context.createCGImage(mask, from: extent) else {
            return originalImage
        }

        // 创建位图上下文用于像素操作
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8

        guard let bitmapContext = CGContext(
            data: nil,
            width: width,
            height: height,
            bitsPerComponent: bitsPerComponent,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
        ) else {
            return originalImage
        }

        // 绘制原始图像到位图上下文
        bitmapContext.draw(originalCGImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        // 获取像素数据
        guard let pixelData = bitmapContext.data else {
            return originalImage
        }

        // 创建掩码位图上下文
        guard let maskBitmapContext = CGContext(
            data: nil,
            width: width,
            height: height,
            bitsPerComponent: bitsPerComponent,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
        ) else {
            return originalImage
        }

        // 绘制掩码图像到位图上下文
        maskBitmapContext.draw(maskCGImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        // 获取掩码像素数据
        guard let maskPixelData = maskBitmapContext.data else {
            return originalImage
        }

        // 处理像素：掩码RGB为0的像素设为透明
        let pixels = pixelData.bindMemory(to: UInt8.self, capacity: width * height * bytesPerPixel)
        let maskPixels = maskPixelData.bindMemory(to: UInt8.self, capacity: width * height * bytesPerPixel)

        for y in 0..<height {
            for x in 0..<width {
                let pixelIndex = (y * width + x) * bytesPerPixel

                // 获取掩码的RGB值
                let maskR = maskPixels[pixelIndex]
                let maskG = maskPixels[pixelIndex + 1]
                let maskB = maskPixels[pixelIndex + 2]

                // 如果掩码RGB都为0（背景），则将像素设为透明
                if maskR == 0 && maskG == 0 && maskB == 0 {
                    pixels[pixelIndex] = 0     // R
                    pixels[pixelIndex + 1] = 0 // G
                    pixels[pixelIndex + 2] = 0 // B
                    pixels[pixelIndex + 3] = 0 // A (透明)
                }
                // 否则保持原始像素不变（人物区域）
            }
        }

        // 创建新的CGImage
        guard let newCGImage = bitmapContext.makeImage() else {
            return originalImage
        }

        // 转换为CIImage并返回
        return CIImage(cgImage: newCGImage)
    }

    // 记录图像中指定坐标点的RGB值
    private func logPixelValues(ciImage: CIImage, context: CIContext, testPoints: [(x: Int, y: Int)], label: String) {
        let extent = ciImage.extent
        let width = Int(extent.width)
        let height = Int(extent.height)

        // 创建位图数据
        let bytesPerPixel = 4
        let bytesPerRow = width * bytesPerPixel
        let bitmapData = UnsafeMutablePointer<UInt8>.allocate(capacity: height * bytesPerRow)
        defer { bitmapData.deallocate() }

        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bitmapContext = CGContext(
            data: bitmapData,
            width: width,
            height: height,
            bitsPerComponent: 8,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
        )

        guard let cgImage = context.createCGImage(ciImage, from: extent),
              let bitmap = bitmapContext else {
            return
        }

        bitmap.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))


        
    }

    // 应用贴纸边缘效果
    private func applyStickerBorder(to image: UIImage, mask: CVPixelBuffer) -> UIImage? {
        guard let ciImage = CIImage(image: image) else { return nil }

        let maskImage = CIImage(cvPixelBuffer: mask)

        // 调整mask尺寸以匹配原图
        let scaledMask = maskImage.transformed(by: CGAffineTransform(
            scaleX: ciImage.extent.width / maskImage.extent.width,
            y: ciImage.extent.height / maskImage.extent.height
        ))

        // 转换为二值掩码
        let binaryMask = scaledMask.applyingFilter("CIColorMatrix", parameters: [
            "inputRVector": CIVector(x: 1000, y: 1000, z: 1000, w: 0),
            "inputGVector": CIVector(x: 1000, y: 1000, z: 1000, w: 0),
            "inputBVector": CIVector(x: 1000, y: 1000, z: 1000, w: 0),
            "inputAVector": CIVector(x: 0, y: 0, z: 0, w: 1)
        ]).applyingFilter("CIColorClamp", parameters: [
            "inputMinComponents": CIVector(x: 0, y: 0, z: 0, w: 0),
            "inputMaxComponents": CIVector(x: 1, y: 1, z: 1, w: 1)
        ])

        // 创建14像素的膨胀mask用于整个边缘
        let morphologyFilter10 = CIFilter(name: "CIMorphologyMaximum")!
        morphologyFilter10.setValue(binaryMask, forKey: kCIInputImageKey)
        morphologyFilter10.setValue(14, forKey: kCIInputRadiusKey)

        guard let dilatedMask10 = morphologyFilter10.outputImage else { return image }

        // 创建12像素的膨胀mask用于白色部分
        let morphologyFilter9 = CIFilter(name: "CIMorphologyMaximum")!
        morphologyFilter9.setValue(binaryMask, forKey: kCIInputImageKey)
        morphologyFilter9.setValue(12, forKey: kCIInputRadiusKey)

        guard let dilatedMask9 = morphologyFilter9.outputImage else { return image }

        // 创建白色边缘mask（12像素膨胀 - 原始mask）
        let whiteEdgeMask = dilatedMask9.applyingFilter("CIDifferenceBlendMode", parameters: [
            kCIInputBackgroundImageKey: binaryMask
        ])

        // 创建黑色边缘mask（14像素膨胀 - 9像素膨胀）
        let blackEdgeMask = dilatedMask10.applyingFilter("CIDifferenceBlendMode", parameters: [
            kCIInputBackgroundImageKey: dilatedMask9
        ])

        // 创建白色边框
        let whiteColor = CIFilter(name: "CIConstantColorGenerator")!
        whiteColor.setValue(CIColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0), forKey: kCIInputColorKey)

        guard let whiteBorder = whiteColor.outputImage?.cropped(to: ciImage.extent) else { return image }

        // 创建黑色边框
        let blackColor = CIFilter(name: "CIConstantColorGenerator")!
        blackColor.setValue(CIColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0), forKey: kCIInputColorKey)

        guard let blackBorder = blackColor.outputImage?.cropped(to: ciImage.extent) else { return image }

        // 应用白色边缘
        let whiteEdge = whiteBorder.applyingFilter("CIBlendWithMask", parameters: [
            kCIInputMaskImageKey: whiteEdgeMask
        ])

        // 应用黑色边缘
        let blackEdge = blackBorder.applyingFilter("CIBlendWithMask", parameters: [
            kCIInputMaskImageKey: blackEdgeMask
        ])

        // 合成：先合成白色边缘，再合成黑色边缘，最后合成到原图
        let whiteEdgeComposed = whiteEdge.composited(over: ciImage)
        let result = blackEdge.composited(over: whiteEdgeComposed)

        let context = CIContext()
        guard let cgImage = context.createCGImage(result, from: result.extent) else { return nil }

        return UIImage(cgImage: cgImage)
    }

    // 保存当前图片
    private func saveCurrentImage() {
        guard let imageToSave = processedImage else { return }

        UIImageWriteToSavedPhotosAlbum(imageToSave, nil, nil, nil)
        alertMessage = "图片已保存到相册"
        showAlert = true
    }

    // 获取处理状态文本
    private func getProcessingText() -> String {
        if isDetectingPerson {
            return "检测人体中..."
        } else if isProcessingStyle {
            return "风格转换中..."
        } else {
            return "处理中..."
        }
    }
}

// 风格选项卡片
struct StyleOptionCard: View {
    let style: ONNXStyleTransfer.StyleType
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 4) {
                // 风格图标
                Image(systemName: getStyleIcon())
                    .font(.system(size: 20))
                    .foregroundColor(isSelected ? .white : .blue)

                Text(style.displayName)
                    .font(Font.custom("PingFang SC", size: 10).weight(.medium))
                    .foregroundColor(isSelected ? .white : .black)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 60)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.blue : Color.white)
                    .shadow(color: .black.opacity(0.1), radius: 3, x: 0, y: 2)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color.blue : Color.gray.opacity(0.3), lineWidth: isSelected ? 2 : 1)
            )
        }
    }

    private func getStyleIcon() -> String {
        switch style {
        case .hayao:
            return "leaf.fill"
        case .shinkai:
            return "cloud.fill"
        case .portraitSketch:
            return "pencil.tip"
        case .cute:
            return "heart.fill"
        }
    }
}

// 图片选择器
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.presentationMode) var presentationMode

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        picker.allowsEditing = false
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UINavigationControllerDelegate, UIImagePickerControllerDelegate {
        let parent: ImagePicker

        init(_ parent: ImagePicker) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
            }
            parent.presentationMode.wrappedValue.dismiss()
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}
